<script lang="ts" setup>
const props = defineProps({
  value: {
    type: [String, Number],
    default: ''
  },
  options: {
    type: Array<Record<string, any>>,
    default: () => []
  },
  itemTitle: {
    type: String,
    default: 'label'
  },
  itemValue: {
    type: String,
    default: 'value'
  },
  fillRow: {
    type: Boolean,
    default: false
  }
})


const emit = defineEmits(['update:value'])

const currentValue = ref(props.value)

watch(() => currentValue.value, (val) => {
  emit('update:value', val)
})
</script>

<template>
  <VBtnToggle :class="['network-type-radio-group pa-0 h-auto border-0', fillRow ? 'w-100' : '']" v-model="currentValue"
    density="compact" mandatory color="primary">
    <template v-for="(item, index) in options" :key="index">
      <VBtn :class="['border-0', fillRow ? 'flex-1-0' : '']" variant="outlined" :max-height="30" base-color="primary"
        type="primary" :value="item[itemValue]">
        {{ item[itemTitle] }}
      </VBtn>
      <div class="network-type-radio-group_divider primary" v-if="index !== options.length - 1"></div>
    </template>
  </VBtnToggle>
</template>

<style lang="scss">
.network-type-radio-group {
  border: 1px solid rgb(var(--v-theme-primary)) !important;

  &_divider {
    background-color: rgb(var(--v-theme-primary)) !important;
    block-size: 30px;
    inline-size: 1px;
  }

  .v-btn__content {
    font-size: 13px;
  }
}
</style>
