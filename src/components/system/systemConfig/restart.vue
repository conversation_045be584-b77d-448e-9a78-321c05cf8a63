<script lang="ts" setup>
import { ElMessage, ElMessageBox } from 'element-plus'
import { getCurrentInstance } from "vue"
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const instance = getCurrentInstance()

const getWaittingModal = (type: any, url?: string) => {
  console.log('[Page] Calling show modal')
  instance?.proxy?.$showWaitingModal?.(type, url)
}

function restart() {
  ElMessageBox.alert(t('SystemConfig.Restart.RestartWarning'), t('SystemConfig.Restart.Warning'), {
    confirmButtonText: t('SystemConfig.Restart.RestartNow'),
    cancelButtonText: t('SystemConfig.Restart.Cancel'),
    type: 'warning',
  }).then(() => {
    $api('', {
      requestType: 300,
    }).then(res => {
      if (res.err_code === -15)
        getWaittingModal(7)

      else if (res.err_code === -2)
        ElMessage.error(t('SystemConfig.Restart.FormatError'))

      else if (res.err_code === -4)
        ElMessage.error(t('SystemConfig.Restart.UnknownRequestType'))

      else if (res.err_code === -9)
        ElMessage.error(t('SystemConfig.Restart.NotLoggedIn'))

      else if (res.err_code === -12)
        ElMessage.error(t('SystemConfig.Restart.SystemBusy'))

      else
        ElMessage.error(t('SystemConfig.Restart.RestartFailed'))
    })
  })
}
</script>

<template>
  <VCard :title="t('SystemConfig.Restart.RestartDevice')">
    <VCardText>
      <div class="d-flex border border-fill-error pa-4 rounded">
        <div class="mr-4 rounded bg-fill-error pa-1 align-self-sm-start">
          <VIcon
            color="error"
            icon="tabler-alert-circle"
          />
        </div>
        <div>
          <div class="text-h5 text-error mb-2">
            {{ t('SystemConfig.Restart.Warning') }}
          </div>
          <div class="text-subtitle-1 text-error">
            {{ t('SystemConfig.Restart.RestartWarning') }}
          </div>
        </div>
      </div>
      <div
        class="mt-4"
        style="display: flex;justify-content: flex-end;"
      >
        <VBtn
          color="error"
          @click="restart"
        >
          {{ t('SystemConfig.Restart.RestartNow') }}
        </VBtn>
      </div>
    </VCardText>
  </VCard>
</template>

<style lang="scss">
.bg-opacity-error {
  background: rgba($color: var(--v-theme-error), $alpha: 8%);
}

.border-fill-error {
  border-color: rgb(var(--v-theme-error)) !important;
}
</style>
