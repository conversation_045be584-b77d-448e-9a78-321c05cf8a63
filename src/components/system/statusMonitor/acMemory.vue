<script lang="ts" setup>
import moment from 'moment'
import { useI18n } from 'vue-i18n'
import { useTheme } from 'vuetify'
import { getAreaChartSplineConfig } from '@core/libs/apex-chart/apexCharConfig'

const props = defineProps({
  memoryList: {
    type: Array,
    default: () => [],
  },
})

const { t } = useI18n()

const timeList = ref<string[]>([])
const memoryList = ref([])

// watch 监听uploadList和downloadList,动态生成表格数据
watch(props.memoryList, (newMainList: any) => {
  let arr = JSON.parse(JSON.stringify(newMainList))
  arr = arr.reverse()
  for (const i in arr) {
    memoryList.value.push(arr[i].usage_percent)

    const time = moment(arr[i].timestamp).format('HH:mm')

    timeList.value.push(time)
  }
}, { immediate: true })

const vuetifyTheme = useTheme()

onMounted(() => {
})

const chartConfig = computed(() => ({
  ...getAreaChartSplineConfig(vuetifyTheme.current.value),

  // 新增平滑曲线配置
  stroke: {
    curve: 'smooth', // 核心平滑配置
    width: 3, // 适当增加线宽
  },

  // 调整填充透明度
  fill: {
    type: 'solid', // 核心修改点
    opacity: 0.2, // 统一透明度
  },
  legend: {
    show: true,
    position: 'bottom', // 位置在底部
    horizontalAlign: 'center', // 水平居中
    itemMargin: {
      horizontal: 8, // 图例项水平间距
      vertical: 4, // 图例项垂直间距
    },
    markers: {
      width: 10, // 颜色标记宽度
      height: 10, // 颜色标记高度
      radius: 5, // 圆角半径
      offsetY: 1, // 垂直偏移量
    },
  },
  colors: [
    '#FF9F43',
  ],
  xaxis: {
    categories: timeList.value,
  },

  // 固定Y轴范围为0-100
  yaxis: {
    min: 0,
    max: 100,
    labels: {
      formatter: (value: number) => `${value}%`,
    },
  },
}))

// 修改 series 为计算属性（关键修改）
const series = computed(() => [
  {
    name: t('SystemStatus.UsageRate'),
    data: memoryList.value,
  },
])
</script>

<template>
  <VueApexCharts
    type="area"
    height="330"
    :options="chartConfig"
    :series="series"
  />
</template>
