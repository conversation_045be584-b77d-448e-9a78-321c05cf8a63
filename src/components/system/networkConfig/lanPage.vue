<script lang="ts" setup>
import { getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'

// 方式1：通过 globalProperties
const instance = getCurrentInstance()
const { t } = useI18n()

const getWaittingModal = (type: any, url?: string) => {
  console.log('[Page] Calling show modal')
  instance?.proxy?.$showWaitingModal?.(type, url)
}

onMounted(() => {
  getLanInfo()
})

const LanInfo: any = reactive({
  dhcpDisabled: '1',
  lanIpAddress: '',
  lanNetmask: '',
  dhcpStartValue: '',
  dhcpMaxNumber: '',
  dhcpLeaseTime: '',
})

// 子网掩码选项列表
const subnetMaskOptions = [
  // A类网络常用
  { title: '*********/8', value: '*********' },
  { title: '***********/9', value: '***********' },
  { title: '***********/10', value: '***********' },
  { title: '***********/11', value: '***********' },
  { title: '***********/12', value: '***********' },
  { title: '***********/13', value: '***********' },
  { title: '***********/14', value: '***********' },
  { title: '***********/15', value: '***********' },

  // B类网络常用
  { title: '***********/16', value: '***********' },
  { title: '*************/17', value: '*************' },
  { title: '*************/18', value: '*************' },
  { title: '*************/19', value: '*************' },
  { title: '*************/20', value: '*************' },
  { title: '*************/21', value: '*************' },
  { title: '*************/22', value: '*************' },
  { title: '*************/23', value: '*************' },

  // C类网络常用
  { title: '*************/24', value: '*************' },
  { title: '***************/25', value: '***************' },
  { title: '***************/26', value: '***************' },
  { title: '***************/27', value: '***************' },
  { title: '***************/28', value: '***************' },
  { title: '***************/29', value: '***************' },
  { title: '***************/30', value: '***************' },
]

const maxAddresses = computed(() => {
  // 计算子网掩码中的主机位数
  const hostBits = calculateHostBits(LanInfo.lanNetmask)

  // 计算最大数量的限额  // 计算最大可用主机数
  const maxHosts = 2 ** hostBits - 2

  console.log('最大可用主机数:', maxHosts)

  let maxNum = maxHosts - (LanInfo.dhcpStartValue - 1)
  if (maxNum < 1)
    maxNum = 1

  return maxNum
})

const maxAddressesLimit = computed(() => {
  // 计算子网掩码中的主机位数
  const hostBits = calculateHostBits(LanInfo.lanNetmask)

  return 2 ** hostBits - 2
})

const calculateHostBits = (subnetMask: string): number => {
  // 将子网掩码转换为二进制字符串
  const binarySubnetMask = subnetMask
    .split('.')
    .map((octet: string) => Number.parseInt(octet).toString(2).padStart(8, '0'))
    .join('')

  // 计算主机位数
  return binarySubnetMask.length - binarySubnetMask.indexOf('0')
}

async function getLanInfo() {
  const dataLan = await $api('', { requestType: 201 })
  if (dataLan.err_code === 0) {
    if (dataLan.info.lan.lanIpAddress !== undefined && dataLan.info.lan.lanIpAddress !== '')
      LanInfo.lanIpAddress = dataLan.info.lan.lanIpAddress

    if (dataLan.info.lan.lanNetmask !== undefined && dataLan.info.lan.lanNetmask !== '')
      LanInfo.lanNetmask = dataLan.info.lan.lanNetmask
    else
      LanInfo.lanNetmask = '*************' // 默认使用/24掩码
  }
  const data = await $api('', { requestType: 204 })
  if (data.err_code === 0) {
    if (data.info.dhcpDisabled !== undefined && data.info.dhcpDisabled !== '')
      LanInfo.dhcpDisabled = data.info.dhcpDisabled

    else
      LanInfo.dhcpDisabled = '0'

    if (data.info.dhcpStartValue !== undefined && data.info.dhcpStartValue !== '')
      LanInfo.dhcpStartValue = data.info.dhcpStartValue

    if (data.info.dhcpMaxNumber !== undefined && data.info.dhcpMaxNumber !== '')
      LanInfo.dhcpMaxNumber = data.info.dhcpMaxNumber

    if (data.info.dhcpLeaseTime !== undefined && data.info.dhcpLeaseTime !== '')
      LanInfo.dhcpLeaseTime = data.info.dhcpDisdhcpLeaseTimeabled
    LanInfo.dhcpLeaseTime = data.info.dhcpLeaseTime
  }
}

const requiredValidator = (value: any) => {
  return !!value || t('NetworkConfig.LAN.Required')
}

const dhcpValidator = (value: any) => {
  // 将value转换为数字进行比较
  const numValue = Number(value)

  // 如果DHCP禁用，直接返回true
  if (LanInfo.dhcpDisabled === '1')
    return true

  // 空值校验
  if (!value && value !== 0)
    return t('NetworkConfig.LAN.Required')

  // 数值有效性和最大值校验
  if (!Number.isNaN(numValue) && numValue >= maxAddressesLimit.value)
    return `${t('NetworkConfig.LAN.MaxValueExceeded')}${maxAddressesLimit.value}`

  return true
}

const dhcpMaxValidator = (value: any) => {
  // 将value转换为数字进行比较
  const numValue = Number(value)

  // 如果DHCP禁用，直接返回true
  if (LanInfo.dhcpDisabled === '1')
    return true

  // 空值校验
  if (!value && value !== 0)
    return t('NetworkConfig.LAN.Required')

  // 数值有效性和最大值校验
  if (!Number.isNaN(numValue) && numValue >= maxAddresses.value)
    return `${t('NetworkConfig.LAN.MaxValueExceededA')}${maxAddresses.value - 1}${t('NetworkConfig.LAN.MaxValueExceededB')}`

  return true
}

// DHCP租期验证器，支持m/h/d单位，确保不小于2分钟
const leaseTimeValidator = (value: any) => {
  // 如果DHCP禁用，直接返回true
  if (LanInfo.dhcpDisabled === '1')
    return true

  // 空值校验
  if (!value && value !== 0)
    return t('NetworkConfig.LAN.Required')

  // 解析带单位的输入值
  let numValue: number
  let unit = ''

  // 匹配数字和单位部分
  const match = String(value).match(/^(\d+)([mhd])?$/i)

  if (!match)
    return t('NetworkConfig.LAN.LeaseTimeFormat')

  const amount = Number.parseInt(match[1])

  unit = (match[2] || '').toLowerCase()

  // 根据单位转换为秒数
  switch (unit) {
    case 'm':
      numValue = amount * 60 // 分钟转秒
      break
    case 'h':
      numValue = amount * 3600 // 小时转秒
      break
    case 'd':
      numValue = amount * 86400 // 天转秒
      break
    default:
      numValue = amount // 默认为秒
  }

  // 检查是否小于2分钟(120秒)
  if (numValue < 120)
    return t('NetworkConfig.LAN.LeaseTimeMinimum')

  return true
}

const lanForm = ref()

const submitIP = () => {
  lanForm.value.validate().then(async (result: any) => {
    if (result.valid) {
      const data = await $api('', {
        requestType: 101,
        data: {
          lan: {
            lanIpAddress: LanInfo.lanIpAddress,
            lanNetmask: LanInfo.lanNetmask,
          },
          dhcp: {
            dhcpDisabled: LanInfo.dhcpDisabled,
            dhcpStartValue: LanInfo.dhcpStartValue,
            dhcpMaxNumber: LanInfo.dhcpMaxNumber,
            dhcpLeaseTime: LanInfo.dhcpLeaseTime,
          },
        },
      })

      if (data.err_code === 0) {
        getWaittingModal(5)
      }
      else {
        const replaceUrl = `http://${LanInfo.lanIpAddress}`

        getWaittingModal(4, replaceUrl)
      }
    }
  })
}

// 添加处理输入事件的方法
const handleIntegerInput = (e: Event, field: string): void => {
  // 获取输入值
  const value = (e.target as HTMLInputElement).value

  // 如果包含小数点，则移除小数部分
  if (value.includes('.')) {
    // 提取整数部分
    const intValue = Number.parseInt(value)

    // 更新数据
    LanInfo[field] = intValue.toString()
  }
}

// 防止输入小数点
const preventDecimal = (e: KeyboardEvent): void => {
  // 阻止输入小数点
  if (e.key === '.' || e.key === ',')
    e.preventDefault()
}
</script>

<template>
  <VCard class="mb-5">
    <VForm ref="lanForm">
      <VCardText>
        <VRow class="match-height">
          <VCol
            cols="12"
            md="6"
          >
            <AppTextField
              v-model="LanInfo.lanIpAddress"
              :label="t('NetworkConfig.LAN.GatewayIP')"
              :placeholder="t('NetworkConfig.LAN.EnterGatewayIP')"
              :rules="[requiredValidator]"
            />
          </VCol>
          <VCol
            cols="12"
            md="6"
          >
            <AppSelect
              v-model="LanInfo.lanNetmask"
              :label="t('NetworkConfig.LAN.SubnetMask')"
              :placeholder="t('NetworkConfig.LAN.EnterSubnetMask')"
              :rules="[requiredValidator]"
              :items="subnetMaskOptions"
              item-title="title"
              item-value="value"
            />
          </VCol>
        </VRow>
        <div class="d-flex justify-space-between align-center mt-4">
          <div>
            <div class="mb-2 text-h6 mt-4 text-on-surface">
              {{ t('NetworkConfig.LAN.DHCPService') }}
            </div>
            <div class="text-subtitle-2">
              {{ t('NetworkConfig.LAN.EnableDHCPDesc') }}
            </div>
          </div>

          <VSwitch
            v-model="LanInfo.dhcpDisabled"
            true-value="0"
            false-value="1"
          />
        </div>
        <VCard
          v-if="LanInfo.dhcpDisabled === '0'"
          class="mt-6"
        >
          <VCardText>
            <VRow class="match-height mb-2">
              <VCol
                cols="12"
                md="6"
              >
                <AppTextField
                  v-model="LanInfo.dhcpStartValue"
                  :label="t('NetworkConfig.LAN.StartValue')"
                  :placeholder="t('NetworkConfig.LAN.EnterStartValue')"
                  type="number"
                  step="1"
                  min="1"
                  :rules="[dhcpValidator]"
                  @keydown="preventDecimal"
                  @input="e => handleIntegerInput(e, 'dhcpStartValue')"
                />
                <div class="text-secondary text-subtitle-2 ms-2">
                  {{ t('NetworkConfig.LAN.StartIPDesc') }}
                </div>
              </VCol>
              <VCol
                cols="12"
                md="6"
              >
                <AppTextField
                  v-model="LanInfo.dhcpMaxNumber"
                  :label="t('NetworkConfig.LAN.MaxCount')"
                  :placeholder="t('NetworkConfig.LAN.EnterMaxCount')"
                  type="number"
                  step="1"
                  min="1"
                  :rules="[dhcpMaxValidator]"
                  @keydown="preventDecimal"
                  @input="e => handleIntegerInput(e, 'dhcpMaxNumber')"
                />
                <div
                  v-if="maxAddresses > 1"
                  class="text-secondary text-subtitle-2 ms-2"
                >
                  {{ t('NetworkConfig.LAN.MaxCountDesc') }} {{ maxAddresses - 1 }}
                </div>
                <div
                  v-else
                  class="text-secondary text-subtitle-2 ms-2"
                >
                  {{ t('NetworkConfig.LAN.MaxCountError') }}
                </div>
              </VCol>
            </VRow>
            <VRow class="match-height mt-2">
              <VCol cols="12">
                <AppTextField
                  v-model="LanInfo.dhcpLeaseTime"
                  :label="t('NetworkConfig.LAN.LeaseTime')"
                  :placeholder="t('NetworkConfig.LAN.EnterLeaseTime')"
                  type="text"
                  :rules="[leaseTimeValidator]"
                />
                <div class="text-secondary text-subtitle-2 ms-2">
                  {{ t('NetworkConfig.LAN.LeaseTimeDesc') }} ({{ t('NetworkConfig.LAN.LeaseTimeFormatHelp') }})
                </div>
              </VCol>
            </VRow>
          </VCardText>
        </VCard>
        <div
          class="mt-4"
          style="display: flex;justify-content: flex-end;"
        >
          <VBtn
            color="primary"
            @click="submitIP"
          >
            {{ t('NetworkConfig.LAN.SaveSettings') }}
          </VBtn>
        </div>
      </VCardText>
    </VForm>
  </VCard>
</template>

<style lang="scss" scoped>
.fontText {
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 13px;

  /* 100% */
}

.desc {
  /* 146.667% */
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: var(--Light-Text-Secondary, text-secondary);

  /* Basic Typography/body-1 */
  font-family: "PingFang SC";
  font-feature-settings: "liga" off, "clig" off;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}
</style>
