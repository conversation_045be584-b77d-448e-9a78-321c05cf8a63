<script setup lang="ts">
import { useI18n } from 'vue-i18n'

const props = defineProps<{
  list: any
}>()

const emits = defineEmits(['wanLan', 'save'])

const { t } = useI18n()

const lastTransitions: any = ref([])

watch(() => props.list, newVal => {
  lastTransitions.value = newVal
}, { deep: true })

const getPaddingStyle = (index: number) => index ? 'padding-block-end: 1.25rem;' : 'padding-block: 1.25rem;'

const updateWanLan = (num: any) => {
  emits('wanLan', {
    type: num,
  })
}

const save = () => {
  emits('save')
}

const formatUnit = (value: string) => {
  // 字节转b/kb/mb/gb
  const bytes = Number.parseInt(value)
  if (bytes < 1024)
    return `${bytes}B`

  if (bytes < 1024 * 1024)
    return `${(bytes / 1024).toFixed(2)}KB`

  if (bytes < 1024 * 1024 * 1024)
    return `${(bytes / 1024 / 1024).toFixed(2)}MB`

  return `${(bytes / 1024 / 1024 / 1024).toFixed(2)}GB`
}
</script>

<template>
  <VCard :title="t('NetworkConfig.WAN.Title')">
    <template #append>
      <VBtn
        color="success"
        variant="tonal"
        class="mr-5"
        @click="updateWanLan(0)"
      >
        {{ t('NetworkConfig.WAN.SetAllLAN') }}
      </VBtn>
      <VBtn
        color="primary"
        @click="updateWanLan(1)"
      >
        {{ t('NetworkConfig.WAN.WanLanExchange') }}
      </VBtn>
    </template>

    <VDivider />
    <VTable class="text-no-wrap transaction-table">
      <thead>
        <tr>
          <th>{{ t('NetworkConfig.WAN.Port') }}</th>
          <th>{{ t('NetworkConfig.WAN.Speed') }}</th>
          <th>{{ t('NetworkConfig.WAN.Send') }}</th>
          <th>{{ t('NetworkConfig.WAN.Receive') }}</th>
        </tr>
      </thead>
      <tbody>
        <tr
          v-for="(transition, index) in lastTransitions"
          :key="index"
        >
          <td
            :style="getPaddingStyle(index)"
            style="padding-inline-end: 1.5rem;"
          >
            <div class="text-high-emphasis text-base">
              {{ transition.typeStr || '-' }}
            </div>
          </td>
          <td
            :style="getPaddingStyle(index)"
            style="padding-inline-end: 1.5rem;"
          >
            <div class="text-high-emphasis text-base">
              <p
                v-if="transition.speed.indexOf('2500') > -1"
                class="m2500"
              >
                {{ transition.speed }}
              </p>
              <p
                v-else-if="transition.speed.indexOf('1000') > -1"
                class="m1000"
              >
                {{ transition.speed }}
              </p>
              <p
                v-else-if="transition.speed.indexOf('100') > -1"
                class="m100"
              >
                {{ transition.speed }}
              </p>
              <p v-else>
                -
              </p>
            </div>
          </td>
          <td
            :style="getPaddingStyle(index)"
            style="padding-inline-end: 1.5rem;"
          >
            <div class="text-high-emphasis text-base">
              <p class="download">
                {{ formatUnit(transition.tx_bytes) }}
              </p>
            </div>
          </td>
          <td
            :style="getPaddingStyle(index)"
            style="padding-inline-end: 1.5rem;"
          >
            <div class="text-high-emphasis text-base">
              <p class="download">
                {{ formatUnit(transition.rx_bytes) }}
              </p>
            </div>
          </td>
        </tr>
      </tbody>
    </VTable>
    <VDivider />
    <div class="d-flex justify-end pa-4">
      <VBtn @click="save">
        {{ t('NetworkConfig.WAN.SaveSettings') }}
      </VBtn>
    </div>
  </VCard>
</template>

<style lang="scss">
.transaction-table {
  &.v-table .v-table__wrapper > table > tbody > tr:not(:last-child) > td,
  &.v-table .v-table__wrapper > table > tbody > tr:not(:last-child) > th {
    border-block-end: none !important;
  }
}

.m2500 {
  color: var(--Color-Primary-primary-400, #165dff);
  font-family: "PingFang SC";
  font-size: 15px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 22px;
}

.m1000 {
  color: var(--Color-Primary-primary-500, #28c76f);
  font-family: "PingFang SC";
  font-size: 15px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 22px;
}

.m100 {
  color: var(--Color-Success-success-500, #ff9f43);
  font-family: "PingFang SC";
  font-feature-settings: "liga" off, "clig" off;
  font-size: 15px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 22px;
}
</style>
