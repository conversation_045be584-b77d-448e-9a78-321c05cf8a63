import type { App } from 'vue'
import { ref } from 'vue'
import WaitingModal from './index.vue'

const waitingModalPlugin = {
  install(app: App) {
    // 使用 shallowRef 避免不必要的深度响应
    const waitingModalRef = ref<{
      open: (message: string, timeout: number, url?: string) => void;
      close: () => void;
    } | null>(null)

    // 提供设置 ref 的方法
    const setWaitingModalRef = (refInstance: any) => {
      waitingModalRef.value = refInstance
      console.log('Modal ref set:', refInstance)
    }

    // 注册组件
    app.component('WaitingModal', WaitingModal)

    // 显示弹窗的逻辑（完全保留原有逻辑）
    const showWaitingModal = (type: number, url?: string) => {
      let message = ''
      let timeout = 60000
      console.log('[Plugin] Show modal triggered:', { type, url })

      switch (type) {
      case 1:
        message = '&nbsp;&nbsp;&nbsp;正在重启中，请等待...'
        timeout = 100000
        break;
      case 2:
        message = '设置成功，请等待...'
        timeout = 10000
        break;
      case 7:
        message = '正在重启中，请等待...'
        timeout = 100000
        break;
      case 8:
        message = '设置成功，请等待...'
        timeout = 2000
        break;
      case 3:
        message = '正在升级中，请等待...'
        timeout = 160000
        break;
      case 4:
        message = '网络正在重置中，请等待...'
        timeout = 10000
        break;
      case 5:
        message = '设置成功，请等待...'
        timeout = 2000
        break;
      case 6:
        message = '设置成功，请等待...'
        timeout = 10000
        break;
      case 9999:
        message = ''
        timeout = 0
        break;
      }

      if (waitingModalRef.value) {
        console.log('[Plugin] Calling modal.open()',message, timeout, url)
        waitingModalRef.value.open(message, timeout, url)
      } else {
        console.error('[Plugin] WaitingModal ref not available')
      }
    }

    const closeWaitingModal = () => {
      if (waitingModalRef.value) {
        console.log('[Plugin] Calling modal.close()')
        waitingModalRef.value.close()
      } else {
        console.error('[Plugin] WaitingModal ref not available')
      }
    }
    // 暴露全局方法和设置方法

    app.config.globalProperties.$closeWaitingModal = closeWaitingModal
    app.config.globalProperties.$showWaitingModal = showWaitingModal
    app.config.globalProperties.$setWaitingModalRef = setWaitingModalRef

    // Provide/inject 方式
    app.provide('closeWaitingModal', closeWaitingModal)
    app.provide('showWaitingModal', showWaitingModal)
    app.provide('setWaitingModalRef', setWaitingModalRef)
  }
}

export default waitingModalPlugin
