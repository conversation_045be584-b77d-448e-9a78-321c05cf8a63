<script lang="ts" setup>
const emits = defineEmits(['close'])

const tabList = [{
  name: '设备ID添加',
  value: 1,
}, {
  name: '批量添加',
  value: 2,
}]

const currentTab = ref(1)
const deviceID = ref('')
const file = ref('')

// 表头
const headersAdd = [
  { title: '设备名称', key: 'name' },
  { title: '设备ID', key: 'id' },
  { title: '注册状态', key: 'status' },
]

// 数据
const productsDataAdd = ref({
  total: 3,
  products: [
    {
      name: 'UBE',
      id: 'ap',
      status: false,
    },
    {
      name: 'UBE',
      id: 'ap',
      status: false,
    },
    {
      name: 'UBE',
      id: 'ap',
      status: false,
    },
  ],
})

// 动态计算表格数据
const productsAdd = computed((): any[] => productsDataAdd.value.products)
const selectedRowsAdd = ref<any[]>([])

// Data table options
const sortBy = ref()
const orderBy = ref()

// Update data table options
const updateOptionsAdd = (options: any) => {
  sortBy.value = options.sortBy[0]?.key
  orderBy.value = options.sortBy[0]?.order
}

const addMore = () => {
  emits('close')
}

const closeMask = () => {
  emits('close')
}
</script>

<template>
  <div class="mainBox">
    <VTabs v-model="currentTab">
      <VTab v-for="item in tabList">
        {{ item.name }}
      </VTab>
    </VTabs>
    <VWindow v-model="currentTab">
      <VWindowItem
        v-for="item in tabList"
        :key="item.value"
        :value="`${item.value}`"
      >
        <div
          v-if="item.value == 1"
          class="mt-4"
        >
          <AppTextField
            v-model="deviceID"
            label="设备ID"
            placeholder="请输入设备ID"
          />
          <div class="btnBox">
            <VBtn
              class="mr-2"
              color="primary"
              @click="addMore"
            >
              添加
            </VBtn>
            <VBtn
              color="secondary"
              @click="closeMask"
            >
              取消
            </VBtn>
          </div>
        </div>
        <div
          v-if="item.value == 2"
          class="mt-4"
        >
          <VFileInput
            v-model="file"
            label="上传文件"
            placeholder="请选择Excel文件"
          />
          <div class="modeText">
            下载Excel模版
          </div>
          <div class="resultText">
            输出结果
          </div>
          <VDataTableServer
            v-model:model-value="selectedRowsAdd"
            :headers="headersAdd"
            :items="productsAdd"
            class="text-no-wrap"
            show-select
            :no-data-text="t('NoData')"
            @update:options="updateOptionsAdd"
          >
            <!-- status -->
            <template #item.status="{ item }">
              <VChip
                color="error"
                label
                size="small"
              >
                未注册
              </VChip>
            </template>
            <template #bottom />
          </VDataTableServer>
          <div class="btnBox">
            <VBtn
              class="mr-2"
              color="primary"
              @click="addMore"
            >
              注册并添加设备
            </VBtn>
            <VBtn
              color="secondary"
              @click="closeMask"
            >
              取消
            </VBtn>
          </div>
        </div>
      </VWindowItem>
    </VWindow>
  </div>
</template>

<style lang="scss" scoped>
.mainBox {
  padding: 24px;
  margin-block-end: 32px;
}

.modeText {
  color: #4080ff;
  font-family: "PingFang SC";
  font-size: 13px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 20px;
  margin-block: 24px;
  margin-inline: 0;
}

.resultText {
  font-family: "PingFang SC";
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 28px;
  margin-block-end: 24px;
}

.btnBox {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-block-start: 32px;
}
</style>
