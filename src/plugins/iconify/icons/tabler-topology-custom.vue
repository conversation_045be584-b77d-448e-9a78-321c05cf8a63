<script setup lang="ts">
defineOptions({
  name: 'TablerTopologyCustom',
})
</script>

<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    stroke-width="1.5"
    stroke-linecap="round"
    stroke-linejoin="round"
  >
    <rect
      x="3"
      y="15"
      width="6"
      height="6"
      rx="2"
    />
    <rect
      x="15"
      y="15"
      width="6"
      height="6"
      rx="2"
    />
    <rect
      x="9"
      y="3"
      width="6"
      height="6"
      rx="2"
    />
    <path d="M6 15V13.5C6 12.5 6.9 11.6 7.9 11.6H16.1C17.1 11.6 18 12.5 18 13.5V15" />
    <path d="M12 9V11.6" />
  </svg>
</template>
