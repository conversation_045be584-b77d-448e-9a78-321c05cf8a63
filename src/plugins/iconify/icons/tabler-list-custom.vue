<script setup lang="ts">
defineOptions({
  name: 'TablerListCustom',
})
</script>

<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    stroke-width="1.5"
    stroke-linecap="round"
    stroke-linejoin="round"
  >
    <ellipse
      cx="14"
      cy="6"
      rx="2"
      ry="2"
    />
    <path d="M4 6H12" />
    <path d="M16 6H20" />
    <ellipse
      cx="8"
      cy="12"
      rx="2"
      ry="2"
    />
    <path d="M4 12H6" />
    <path d="M10 12H20" />
    <ellipse
      cx="17"
      cy="18"
      rx="2"
      ry="2"
    />
    <path d="M4 18H15" />
    <path d="M19 18H20" />
  </svg>
</template>
