{"Dashboard": "Dashboard", "SystemManagement": "System Management", "StatusMonitoring": "System Status", "NetworkConfiguration": "Network Configuration", "SystemConfiguration": "System Settings", "WirelessOptimization": "Wireless Optimization", "DeviceManagement": "Device Management", "DeviceDiscovery": "<PERSON>ce Discovery", "APManagement": "AP Management", "ConfigurationManagement": "Configuration Management", "TemplateManagement": "Template Management", "APConfigurationDeployment": "AP Provisioning", "NetworkManagement": "Network Management", "NetworkTopology": "Network Topology", "NetworkDataStatistics": "Network Statistics", "APData": "AP Metrics", "TerminalData": "Client Metrics", "NetworkOverview": "Network Overview", "NetworkStatus": "Network Health", "DeviceStatus": "Device Health", "NetworkOperations": "Network Operations", "NetworkEvents": "Event Log", "WelcomeMessage": "Welcome", "Someone": "PUSR", "IoT": "PUSR", "CommercialNetwork": "Business NET", "SimplifyComplexity": "Complexity，Simplified", "EasyControl": "Control Your Network with Ease", "Username": "Username", "Password": "Password", "RememberAccount": "Remember Account", "Login": "<PERSON><PERSON>", "Tip": {"EnterPassword": "Enter Password", "EnterAccount": "Enter Account", "errorStr": "The selected device models are inconsistent. Please choose devices of the same model or select a model first."}, "PleaseSelect": "Please Select", "Unknown": "Unknown", "BandwidthUsage": "Bandwidth Usage", "TotalUsageAllDevices": "Total Usage of All Devices", "Notifications": "Event Notifications", "NewNotifications": "New Notifications", "APOnline": "AP Online", "APOffline": "AP Offline", "APHighTemperature": "AP High Temperature Event", "IgnoreAlert": "Igno<PERSON>", "Ignored": "Ignored", "NoEvents": "No Events", "HighTemperatureWarning": "An alert event is generated if any of CPU/WiFi2G/WiFi5G exceeds 100 degrees.", "Logout": "Logout", "5": "5", "10": "10", "20": "20", "25": "25", "50": "50", "100": "100", "$vuetify": {"badge": "Badge", "noDataText": "No data available", "close": "Close", "open": "open", "loading": "loading", "carousel": {"ariaLabel": {"delimiter": "delimiter"}}, "dataFooter": {"itemsPerPageText": "Items per page:", "itemsPerPageAll": "All", "pageText": "{0}-{1} of {2}", "firstPage": "First Page", "prevPage": "Previous Page", "nextPage": "Next Page", "lastPage": "Last Page"}, "pagination": {"ariaLabel": {"root": "root", "previous": "previous", "first": "first", "last": "last", "next": "next", "currentPage": "currentPage", "page": "page"}}, "input": {"clear": "clear", "appendAction": "appendAction", "prependAction": "prependAction", "counterSize": "counterSize", "otp": "otp"}, "fileInput": {"counterSize": "counterSize"}, "rating": {"ariaLabel": {"item": "item"}}}, "OnlineAP": "Online AP", "OfflineAP": "Offline AP", "TerminalCount": "Terminal Count", "UnitDevice": "Units", "AC": "AC", "AP": "AP", "Router": "Router", "Offline": "Offline", "Name": "Name", "IPAddress": "IP Address", "AccessType": "Access Type", "Signal": "Signal", "LeaseRemaining": "Lease Remaining", "HistoryTraffic": "Traffic", "All": "All", "OnlineClients": "Online Clients", "NoData": "No Data", "DeviceLoadRanking": "<PERSON><PERSON>", "Icon": "Icon", "Device1": "Device 1", "Terminal": "Terminal", "AlarmList": "Alarm List", "Content": "Content", "DeviceName": "Device Name", "Type": "Type", "Time": "Time", "DeviceOffline": "Device Offline", "EventType1": "Event Type 1", "TodayAlarm": "Today's Alarms", "CriticalEvent": "Critical Event", "HighEvent": "High Event", "MediumEvent": "Medium Event", "LowEvent": "Low Event", "DeviceEventCount": "Device Event Count", "View": "View", "RouterTotalRate": "Router Total Rate", "Upload": "Upload", "Download": "Download", "CountUnit": "units", "DataFetchFailed": "Data fetch failed", "RequestError": "Request error", "SystemStatus": {"Title": "System Status", "CurrentSettings": "Current System Settings", "Hostname": "Hostname", "Model": "Model", "FirmwareVersion": "Firmware Version", "IPAddress": "IP Address", "RunningTime": "Running Time", "MACAddress": "MAC Address", "Days": "Days", "UsageRate": "Usage Rate", "APCount": "AP Count", "SerialNumber": "Serial Number", "NetworkAccess": "Network Access"}, "NetworkTraffic": {"Title": "Network Traffic", "Subtitle": "Real-time Bandwidth Monitoring", "Upload": "Upload", "Download": "Download", "Units": {"BytesPerSecond": "B/s", "KBPerSecond": "KB/s", "MBPerSecond": "MB/s", "GBPerSecond": "GB/s"}}, "CPUUsage": {"Title": "CPU Usage", "Subtitle": "Real-time CPU Load Monitoring"}, "MemoryUsage": {"Title": "Memory Usage", "Subtitle": "Real-time Memory Usage Monitoring"}, "NoInternet": "No Internet", "Internet": "Internet", "Local": "Local", "NetworkConfig": {"Tabs": {"PortStatus": "Port Status", "NetworkSettings": "Network Settings", "IPMapping": "IP Mapping", "PortMapping": "Port Mapping", "LANSettings": "LAN Settings", "DHCPIPBinding": "DHCP IP Binding"}, "Modes": {"RouterMode": "Router Mode", "APMode": "AP Mode", "RelayMode": "Relay Mode"}, "LAN": {"GatewayIP": "Gateway IP", "EnterGatewayIP": "Enter Gateway IP", "SubnetMask": "Subnet Mask", "EnterSubnetMask": "Enter Subnet Mask", "DHCPService": "DHCP Service", "EnableDHCPDesc": "Enable DHCP service to automatically assign IP addresses to connected devices", "StartValue": "Start Value", "EnterStartValue": "Enter Start Value", "StartIPDesc": "Starting IP address suffix for DHCP allocation", "MaxCount": "Max Count", "EnterMaxCount": "Enter Max <PERSON>", "MaxCountDesc": "Maximum number of assignable IPs, the max is", "MaxCountError": "Exceeds the maximum allowable quantity, please modify the starting value", "LeaseTime": "Lease Time", "EnterLeaseTime": "Enter lease time, e.g. 2h", "LeaseTimeDesc": "DHCP IP address lease time", "LeaseTimeMinimum": "Lease time must be greater than 2 minutes (2m)", "LeaseTimeFormat": "Incorrect format, please use number with unit, e.g. 30m", "LeaseTimeFormatHelp": "Format: number+unit, units: m(minutes), h(hours), d(days), e.g. 30m, 12h, 1d", "SaveSettings": "Save Settings", "Required": "This field is required", "MaxValueExceeded": "Maximum value cannot exceed", "MaxValueExceededA": "Maximum quantity is", "MaxValueExceededB": ", please modify", "EnterInteger": "Please enter an integer", "RangeLimit": "Please enter an integer between 1 and 6"}, "DHCP": {"Title": "DHCP IP Binding", "AddDevice": "Add <PERSON>", "AddDHCPBinding": "Add DHCP IP Binding", "DeviceName": "Device Name", "DeviceNamePlaceholder": "Example: Office Printer", "IPAddress": "IP Address", "IPAddressPlaceholder": "Example: **************", "MACAddress": "MAC Address", "MACAddressPlaceholder": "Example: AA:BB:CC:DD:EE:FF", "Add": "Add", "Cancel": "Cancel", "InvalidIPv4": "Please enter a valid IPv4 address", "InvalidMAC": "Please enter a valid MAC address", "InvalidDeviceName": "Only English letters, numbers and !@#$%-_+= are allowed, up to 32 characters, no spaces or Chinese characters.", "DHCPBindingDesc": "When you specify a reserved IP address for a client on your LAN, the client will always receive the same IP address when accessing the DHCP server. You can assign reserved IP addresses to computers or servers that require permanent IP settings.", "DHCPBindingNote": "Note: The configured client must reconnect to the router for the settings to take effect.", "DHCPBindingError": "Please check if this IP address or MAC address is already bound"}, "WAN": {"Title": "Port Details", "SetAllLAN": "Set All as LAN Ports", "WanLanExchange": "WAN / LAN Exchange", "Port": "Port", "Speed": "Speed", "Send": "Send", "Receive": "Receive", "SaveSettings": "Save Settings", "Speeds": {"2500M": "2.5G", "1000M": "1000M", "10_100M": "10/100M"}, "NotConnected": "Not Connected"}, "Network": {"DynamicIP": "Dynamic IP Address (DHCP)", "DynamicIPDesc": "Device automatically obtains IP address, subnet mask, gateway, and DNS address from the upper server.", "PPPoE": "Broadband Dial-up (PPPoE)", "PPPoEDesc": "Please enter the username and password provided by your ISP (Telecom, Unicom, Mobile, etc.) to connect to the network via dial-up.", "StaticIP": "Static IP Address", "StaticIPDesc": "Please set a fixed WAN IP address, subnet mask, gateway, and DNS address.", "MACClone": "MAC Address Clone", "MACCloneDesc": "Simulate a specific device's MAC address to adapt to certain ISP binding requirements.", "CurrentMAC": "Current MAC Address", "CloneMAC": "Clone MAC Address", "EnterCloneMAC": "Enter Clone MAC Address", "CloneMACRequired": "Clone MAC address cannot be empty", "CloneMACInvalid": "Invalid MAC address format", "InternetAccount": "Internet Account", "EnterInternetAccount": "Enter Internet Account", "InternetAccountRequired": "Internet account cannot be empty", "InternetAccountInvalid": "Invalid Internet account format", "InternetPassword": "Internet Password", "EnterInternetPassword": "Enter Internet Password", "InternetPasswordRequired": "Internet password cannot be empty", "InternetPasswordInvalid": "Invalid password format", "IPAddress": "IP Address", "EnterIPAddress": "Enter IP Address", "IPAddressRequired": "Please enter IP address", "IPAddressInvalid": "Invalid IP address", "Subnet": "Subnet Mask", "EnterSubnet": "Enter Subnet Mask", "SubnetRequired": "Please enter subnet mask", "SubnetInvalid": "Invalid subnet mask", "Gateway": "Gateway Address", "EnterGateway": "Enter Gateway Address", "GatewayRequired": "Please enter gateway address", "GatewayInvalid": "Invalid gateway address", "PrimaryDNS": "Primary DNS Server", "EnterPrimaryDNS": "Enter Primary DNS Server", "PrimaryDNSRequired": "Please enter primary DNS server", "PrimaryDNSInvalid": "Invalid primary DNS server", "SecondaryDNS": "Secondary DNS Server", "EnterSecondaryDNS": "Enter Secondary DNS Server", "SecondaryDNSRequired": "Please enter secondary DNS server", "SecondaryDNSInvalid": "Invalid secondary DNS server", "RecommendedDNS": "Recommended Alibaba Cloud DNS: *********, *********", "SaveSettings": "Save Settings", "AccountIncorrect": "Internet account is incorrect", "PasswordIncorrect": "Internet password is incorrect", "Error": "An error occurred, please try again", "RelayModeError": "An error occurred, please set the system mode to relay mode"}, "IP": {"Title": "IP Mapping", "AddIPMapping": "Add IP Mapping", "DeviceIP": "Device IP", "EnterDeviceIP": "Please enter the internal device IP", "MappingIP": "Mapping IP", "EnterMappingIP": "Please enter the public mapping IP (usually AC WANIP)", "CommunicationHostIP": "Communication host IP (optional)", "EnterCommunicationHostIP": "Please enter the external host IP", "InternalIP": "Internal IP", "EnterInternalIP": "System automatically assigns IP", "Apply": "Apply", "Close": "Close", "Required": "This field is required", "InvalidIP": "Please enter a valid IP address", "Description": "Map the private IP address of the internal network device to a public IP, allowing external devices to access it directly.", "Points": {"Point1": "Connect the device to the LAN port; its IP address will be mapped to the WAN side so that external devices can access the device directly via the mapping IP.", "Point2": "After specifying the communication host IP, LAN devices can access the specified WAN-side host using the internal IP, enabling two-way communication.", "Point3": "Only one IP mapping is supported.", "Point4": "Device IP and mapped IP cannot conflict with LAN/WAN network segments."}}, "Port": {"Title": "Port Mapping", "AddPortMapping": "Add Port Mapping", "DeviceIP": "Device IP", "EnterDeviceIP": "Enter Device IP", "WANPCIP": "WAN PC IP", "EnterWANPCIP": "Enter WAN PC IP", "Port": "Port", "EnterPort": "Enter Port", "AddRule": "Add Rule", "Status": "Status", "Actions": "Actions", "Enable": "Enable", "Disable": "Disable", "Delete": "Delete", "Description": "Typical application scenario: Allows device A on the WAN port to access a specified service port of device B on the LAN side via the router's WAN IP.", "Points": {"Point1": "You must disable IP mapping first, otherwise port mapping may not work.", "Point2": "The device IP should be an IP in the LAN segment, for example: **************.", "Point3": "The WAN-side PC IP should be set to device <PERSON>'s IP (it must be in the same segment as the WAN); if the WAN port doesn’t have a public IP, use a static IP instead.", "Point4": "The port can be a single port (e.g., 80) or a continuous port range (e.g., 8080-8090).", "Point5": "The configuration takes effect immediately. Device A can access device B’s service port directly via the WAN IP and port."}, "Required": "This field is required", "InvalidIP": "Please enter a valid IPv4 address", "InvalidPort": "Please enter a valid port", "Occupied": "Port is already occupied", "InvalidPortRange": "Invalid port range"}}, "SystemConfig": {"Tabs": {"SystemSettings": "System Settings", "SecurityManagement": "Security Management", "UpgradeBackup": "Upgrade & Backup", "RestartDevice": "<PERSON><PERSON>", "FactoryReset": "Factory Reset"}, "System": {"CurrentSystemTime": "Current System Time", "SyncBrowserTime": "Sync Browser Time", "SyncNTPServerTime": "Sync NTP Server Time", "Timezone": "Timezone", "SelectTimezone": "Select Timezone", "SaveSettings": "Save Settings", "TimezoneSetSuccess": "Timezone set successfully", "BrowserTimeSyncSuccess": "Browser time synchronized successfully"}, "Security": {"LoginSettings": "<PERSON><PERSON>", "Username": "Username", "EnterUsername": "<PERSON><PERSON> Username", "USRCloud": "USR Cloud", "USRCloudDesc": "The device will connect to remote cloud management services, allowing users to manage the device through the cloud. Remote management requires registration and device binding on the USR Cloud platform.", "USRAcap": "AC Management", "USRAcapDesc": "Control AP management permissions. If disabled, AP management will be disabled, and default is enabled.", "OriginalPassword": "Original Password", "EnterOriginalPassword": "Enter Original Password", "NewPassword": "New Password", "NewPasswordDesc": "Cannot be empty, maximum 15 characters. Recommended to use letters + numbers + special characters for better security.", "ConfirmNewPassword": "Confirm New Password", "EnterNewPassword": "Enter New Password", "PleaseConfirmNewPassword": "Confirm New Password", "SaveSettings": "Save Settings", "PasswordMismatch": "The two passwords do not match", "SettingsSuccess": "Setting<PERSON> saved successfully", "IncorrectCredentials": "Please enter the correct username and password", "SamePassword": "Error, please enter a new password different from the original", "SettingsFailed": "Settings failed"}, "Restart": {"RestartDevice": "<PERSON><PERSON>", "Warning": "Warning", "RestartWarning": "Restarting the device will interrupt all connections. Please make sure no important operations are in progress.", "RestartNow": "Restart Now", "Cancel": "Cancel", "RestartSuccess": "Restart successful", "FormatError": "Format error", "UnknownRequestType": "Unknown request type", "NotLoggedIn": "Not logged in", "SystemBusy": "System busy", "RestartFailed": "Restart failed"}, "Reset": {"FactoryReset": "Factory Reset", "Warning": "Warning", "ResetWarning": "Factory reset will clear all configuration information, including network settings, user passwords, etc., and the device will return to its initial state. This operation is irreversible, please proceed with caution.", "ResetNow": "Reset Now", "Cancel": "Cancel", "ResetSuccess": "Reset successful", "FormatError": "Format error", "UnknownRequestType": "Unknown request type", "NotLoggedIn": "Not logged in", "SystemBusy": "System busy", "ResetFailed": "Reset failed"}, "Update": {"UpgradeMethod": "Upgrade Method", "AutoUpgrade": "Auto Upgrade", "ManualUpgrade": "Manual Upgrade", "RestartAfterUpgrade": "Restart after upgrade", "RestartAfterUpgradeDesc": "When enabled, the firmware will automatically upgrade after download completion.", "FirmwareUpgrade": "Firmware Upgrade", "Desc": "Flash new firmware. Click Browse to select a compatible firmware file for uploading to refresh the current system. If you want to restore factory settings during the upgrade, check the Restore Factory Settings option. Unchecking it means the system will retain the current configuration after the upgrade.", "SelectFirmwareFile": "Select Firmware File", "UploadFirmware": "Upload Firmware", "PleaseUploadFirmware": "Please upload firmware file", "UploadSuccess": "Upload successful", "CurrentVersion": "Current Version", "LatestVersion": "Latest Version", "UpgradeConfirmation": "Check Update", "Confirm": "Confirm", "Cancel": "Cancel", "UpgradeSuccess": "Upgrade successful", "AlreadyLatestVersion": "Current version is already the latest", "FirmwareDownloaded": "Firmware has been downloaded, do you want to download again?", "Yes": "Yes", "No": "No", "DownloadFailed": "Firmware download failed", "ServerConnectionFailed": "Server connection failed", "FileVerificationError": "File verification error", "GetFirmwareInfoFailed": "Failed to get firmware information", "BackupRestore": "System Backup & Restore", "BackupRestoreDesc": "You can quickly set up router configurations through backup/restore operations.", "ImportBackup": "Start Restore", "SelectFile": "Select File", "NoFileSelected": "No file selected", "BackupConfig": "Backup Configuration", "ImportSuccess": "Import successful", "ClickUpdate": "Click Update", "DownloadIng": "DownloadIng"}}, "Device": {"AP": {"Title": "AP Management", "Name": "Name", "DeviceName": "Device Name", "DeviceType": "Device Type", "DeviceTypeDesc": "Device Type", "Model": "Model", "SerialNumber": "SN", "IPAddress": "IP Address", "MACAddress": "MAC Address", "FirmwareVersion": "Firmware Version", "RunningTime": "Running Time", "Actions": "Actions", "SelectModel": "Select Model", "SelectOperation": "Select Operation", "SelectAP": "Select AP", "Online": "Online", "Offline": "Offline", "Operations": {"Restart": "<PERSON><PERSON>", "FactoryReset": "Factory Reset", "Upgrade": "Upgrade", "Delete": "Delete", "BlinkLED": "Blink LED", "ExportSN": "Export SN"}, "NoFileSelected": "No File Selected", "SelectFile": "Select File", "RestoreFactory": "Restore Factory", "Blink": "Blink", "Normal": "Normal", "Confirm": "Confirm Execute", "RemoteConfig": "Remote Config", "DeviceManagement": "Device Management", "RestartDevice": "<PERSON><PERSON>", "DeleteDevice": "Delete Device", "NoSN": "No SN", "RestartConfirm": "Are you sure you want to restart this device?", "DeleteConfirm": "Are you sure you want to delete this device?", "Tip": "Tip", "Cancel": "Cancel", "RestartSuccess": "Restart Success", "RestartCancelled": "<PERSON><PERSON>ed", "DeleteSuccess": "Delete Success", "DeleteCancelled": "Delete Cancelled", "ExecuteSuccess": "Execute Success", "UpgradeSuccess": "Upgrade Success", "InternetStatus": "Internet Status", "WorkingMode": "Working Mode", "RouterMode": "Router Mode", "APMode": "AP Mode", "LANIP": "LAN IP", "LAN1": "LAN1", "LAN2": "LAN2", "Connected": "Connected", "Disconnected": "Disconnected", "InterfaceRate": "Interface Rate", "NoDataYet": "No Data Yet", "DeviceInfo": "Device Info", "WirelessStatus": "Wireless Status", "WorkMode": "Work Mode", "RouterModeAPModeDesc": "Working Mode Switch Guide", "RouterModeDesc": "Router Mode: <PERSON><PERSON> works as a router, connecting to the Internet", "APModeDesc": "AP Mode: Device works as a wireless access point", "SSID": "SSID", "EncryptionType": "Encryption Type", "Password": "Password", "Status": "Status", "APIsolation": "AP Isolation", "Protocol": "Protocol", "CountryCode": "Country Code", "Channel": "Channel", "BandWidth": "Bandwidth", "TransmitPower": "Transmit Power", "MaxConnections": "Max Connections", "Enable": "Enable", "Disable": "Disable", "Save": "Save", "ModeSwitchFailed": "Mode Switch Failed", "EnterEightDigitPassword": "Please enter at least 8-digit password", "NoConfigNeeded": "No Config Needed", "ConfigSuccess": "Config Success", "VerificationTimeout": "Verification Timeout", "DeployTimeout": "Deploy Timeout", "UnknownError": "Unknown Error", "GetStatusFailed": "Get Status Failed", "All": "All", "Required": "This field is required", "InvalidIPFormat": "Please enter a valid IP address format", "IPRangeError": "Each segment of IP must be between 0-255", "IPLastByteError": "Last segment of IP cannot be 0 or 255", "NotPrivateIP": "IP address must be a private network address (10.x.x.x, 172.16-31.x.x, or 192.168.x.x)"}, "Remote": {"DeviceInfo": "Device Information", "Status": "Status", "DeviceType": "Device Type", "SoftwareVersion": "Software Version", "IPAddress": "IP Address", "MACAddress": "MAC Address", "OnlineTime": "Online Time", "RunningTime": "Running Time", "ModeSwitch": "Mode Switch", "RouterMode": "Router Mode", "RouterModeDesc": "Set WAN port to use DHCP dynamic IP, static IP, or PPPoE dial-up, and LAN port to automatically assign IPs", "APMode": "AP Mode", "APModeDesc": "Bridge WAN and LAN ports together and disable automatic IP assignment", "Cancel": "Cancel", "SaveSettings": "Save Settings", "ModeSwitchFailed": "Mode switch failed", "GetDeviceInfoFailed": "Failed to get device information"}, "List": {"DeviceDiscovery": "<PERSON>ce Discovery", "ScanLocalNetwork": "Scan Local Network", "BatchAddDevices": "<PERSON><PERSON> Add Devices", "Model": "Model", "SerialNumber": "Serial Number/SN", "IPAddress": "IP Address", "MACAddress": "MAC Address", "DiscoveryTime": "Discovery Time", "Status": "Status", "Actions": "Actions", "Online": "Online", "Offline": "Offline", "AddDevice": "Add <PERSON>"}}, "Config": {"Mode": {"BasicSettings": "Basic Settings", "TemplateManagement": "Template Management", "NewTemplate": "New Template", "EditTemplate": "Edit Template", "CopyTemplate": "Copy Template", "DeleteTemplate": "Delete Template", "DeployConfig": "Deploy Template Configuration", "NewConfigTemplate": "New Config Template", "DeleteConfirm": "Are you sure you want to delete template {name}?", "Tips": "Tips", "Confirm": "Confirm", "Cancel": "Cancel", "DeleteSuccess": "Deleted successfully", "CopySuccess": "<PERSON><PERSON>d successfully", "CopyFailed": "Co<PERSON> failed", "SaveSuccess": "Saved successfully", "SaveFailed": "Save failed", "PasswordLengthError": "Please enter at least 8 characters for password!", "SelectModel": "Select Model", "SSIDConfig": "SSID Configuration", "AdvancedConfig": "Advanced Config (Optional)", "RoamingConfig": "Roaming Config (Optional)", "TemplateName": "Template Name", "EnterTemplateName": "Please enter template name", "TemplateNameLengthError": "Template name must be between 1 and 64 characters", "Remark": "Remark", "SelectRemark": "Please select remark", "RemarkLengthError": "Remark must be between 0 and 128 characters", "SelectAPModel": "Select AP Model", "Next": "Next", "SaveTemplate": "Save Template", "None": "None", "EnterSSID": "Please enter SSID", "SelectEncryption": "Please select encryption type", "EnterPassword": "Please enter password", "On": "On", "Off": "Off", "SelectProtocol": "Please select protocol", "SelectCountry": "Please select country", "SelectChannel": "Please select channel", "SelectBandwidth": "Please select bandwidth", "SelectTxPower": "Please select TX power", "SelectNetworkType": "Please select network type", "SelectSpeedLimit": "Please select speed limit", "EnterUpstreamLimit": "Please enter Uplink Limit", "EnterDownstreamLimit": "Please enter Downlink Limit", "SelectRoamingProtocol": "Please select roaming protocol", "SSIDLoadBalancing": "SSID Load Balancing", "SSIDLoadBalancingPlaceholder": "Integer, 200ms or 50～1000ms", "DisconnectWeakSignal": "Disconnect Weak Signal Terminal", "DisconnectWeakSignalPlaceholder": "Integer, -75dbm or -100～-50", "IgnoreWeakSignal": "Ignore Weak Signal Detection", "IgnoreWeakSignalPlaceholder": "Integer, -85dbm or -100～-50", "IgnoreExcessiveRetransmission": "Ignore Excessive Retransmission", "IgnoreExcessiveRetransmissionPlaceholder": "Integer, 50% or 0～100", "NumericRangeValidation": "Integer, 50% or 0～100", "Isolate": "Client Isolation", "Hide": "<PERSON>de", "NetworkConfig": "Network Configuration", "NetworkType": "Network Type", "SpeedLimit": "Speed Limit", "UpstreamLimit": "Uplink Limit", "DownstreamLimit": "Downlink Limit", "QuickRoaming": "Quick Roaming", "RoamingProtocol": "Roaming Protocol", "SSIDLengthError": "SSID length must be between 1 and 32 characters", "ApplicableModel": "Applicable Model", "Wi-Fi": "Wi-Fi", "CreationTime": "Update Time", "DeviceCount": "<PERSON><PERSON>", "Description": "Description", "Actions": "Actions", "GetTemplatesFailed": "Failed to get templates list", "WirelessSettings2G": "2.4G Wireless Settings", "WirelessSettings5G": "5G Wireless Settings", "SSID": "SSID", "EncryptionType": "Encryption Type", "Password": "Password", "Protocol": "Protocol", "Country": "Country", "Channel": "Channel", "Bandwidth": "Bandwidth", "TxPower": "TX Power", "EnableWiFi": "Enable Wi-Fi"}, "AP": {"DeviceName": "Device Name", "Model": "Model", "SerialNumber": "Serial Number", "txPower.penetration": "Penetration", "txPower.normal": "Normal", "txPower.saving": "Energy Saving", "IPAddress": "IP Address", "MACAddress": "MAC Address", "Status": "Status", "SelectDevices": "Please select devices", "SelectSameModel": "Please select devices of the same model", "TemplateManagement": "Template Management", "NewTemplate": "New Template", "EditTemplate": "Edit Template", "NewConfigTemplate": "New Config Template", "DualBandUnify": "Dual Band Unification", "DualBandUnifyHint": "(Merge 2.4G/5G. Prioritize 5G connection)", "TemplateName": "Template Name", "EnterTemplateName": "Please enter template name", "TemplateNameLengthError": "Template name must be between 1 and 64 characters", "Remark": "Remark", "SelectRemark": "Please select remark", "RemarkLengthError": "Remark must be between 0 and 128 characters", "SelectAPModel": "Select AP Model", "EnterAPModel": "Please select AP model", "EnterSSID": "Please enter SSID", "SSIDLengthError": "SSID length must be between 1 and 32 characters", "SelectEncryption": "Please select encryption type", "EnterPassword": "Please enter password", "PasswordLengthError": "Please enter at least 8 characters for password!", "On": "On", "Off": "Off", "Isolate": "Client Isolation", "SelectProtocol": "Please select protocol", "SelectCountry": "Please select country", "SelectChannel": "Please select channel", "SelectBandwidth": "Please select bandwidth", "SelectTxPower": "Please select TX power", "NetworkType": "Network Type", "SelectNetworkType": "Please select network type", "SpeedLimit": "Speed Limit", "SelectSpeedLimit": "Please select speed limit", "UpstreamLimit": "Uplink Limit", "EnterUpstreamLimit": "Please enter Uplink Limit", "DownstreamLimit": "Downlink Limit", "EnterDownstreamLimit": "Please enter Downlink Limit", "QuickRoaming": "Quick Roaming", "RoamingProtocol": "Roaming Protocol", "SelectRoamingProtocol": "Please select roaming protocol", "WirelessSettings2G": "2.4G Wireless Settings", "WirelessSettings5G": "5G Wireless Settings", "SelectModel": "Select Model", "SSIDConfig": "SSID Configuration", "AdvancedConfig": "Advanced Config (Optional)", "RoamingConfig": "Roaming Config (Optional)", "Next": "Next", "SaveTemplate": "Save Template", "Cancel": "Cancel", "SaveSuccess": "Saved successfully", "SaveFailed": "Save failed", "SearchDevice": "Please enter device name", "SelectPreConfigTemplate": "Select Pre-config Template", "SelectDistributionMethod": "Select Distribution Method", "SelectDistributionTime": "Select Distribution Time", "YearMonthDayTime": "Year / Month / Day  --:--", "ConfigTimeout": "Configuration timeout", "ConfigAllSuccess": "All configurations successful", "ConfigPartialSuccess": "Partial configurations successful", "RequestFailed": "Request failed", "ConfigSelection": "01 Config Selection", "DistributionMethod": "02 Distribution Method", "CreateNewTemplate": "No template? Create one", "ConfigDistributionComplete": "Configuration Distribution Complete", "ConfigDistributing": "Distributing Configuration...", "TotalDistribution": "Total Distribution", "Success": "Success", "Failed": "Failed", "Online": "Online", "Offline": "Offline", "All": "All", "APConfigDistribution": "AP Config Distribution", "SendTemplate": "Send Template", "Previous": "Previous", "Confirm": "Confirm", "ConfirmDeploy": "Confirm Deploy", "Complete": "Complete", "ExportLog": "Export Log (CSV)", "ModelType": "model device", "PleaseSelectModel": "Please select model", "ConfigStatus": "Configuration Status", "ConfigTime": "Configuration Time", "NotStarted": "Not Started", "ConfigDistributionRecord": "Configuration Distribution Record", "DeployConfig": "Deploy Template Configuration"}, "Device": {"DeviceName": "Device Name", "DeviceType": "Device Type", "DeviceModel": "Device Model", "OnlineStatus": "Online Status", "Actions": "Actions", "DeviceList": "Device List", "DeviceTotal": "Device Total", "Online": "Online", "Offline": "Offline", "DeviceDetails": "<PERSON>ce Det<PERSON>"}}, "Network": {"Tupo": {"NetworkTopology": "Network Topology", "Internet": "Internet", "Terminal": "Terminal", "ExportTopology": "Export Topology", "ExportAs": "Export As", "Export": "Export", "Cancel": "Cancel", "Type": "Type", "IPAddress": "IP Address", "MACAddress": "MAC Address", "RunningTime": "Running Time", "WiredConnection": "Wired Connection", "WirelessConnection": "Wireless Connection", "PNGImage": "PNG Image", "PDFFile": "PDF File", "JSONFile": "JSON File", "CSVFile": "CSV File", "Parent": "Parent", "Name": "Name", "Level": "Level", "ID": "ID", "NotAvailable": "Not Available"}}, "Delete": "Delete", "Action": "Action", "speedLimit": {"off": "Off", "static": "Static Limit"}, "template": {"immediate": "Immediate Dispatch"}, "dateFilter": {"day": "1 Day", "week": "1 Week", "month": "1 Month", "year": "1 Year"}, "eventLevel": {"all": "All", "notification": "Notif", "minor": "Minor", "normal": "Normal", "severe": "Severe"}, "eventType": {"fault": "Typical Fault", "load": "System Load"}, "terminal": {"online": "Online", "offline": "Offline", "wired": "Wired", "wireless": "Wireless"}, "event": {"unhandled": "Pending", "handled": "Handled", "ignored": "Ignored"}, "date": {"daily": "Daily", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "txPower": {"penetration": "Wall Penetration", "normal": "Normal", "saving": "Energy Saving"}, "NetworkEvent": {"Title": "Network Events Count", "EventList": "Event List", "EventLevel": "Event Level", "EventType": "Event Type", "EventName": "Event Name", "EventCount": "Event Count", "AffectedDeviceCount": "Affected Device Count", "AffectedTerminalCount": "Affected Terminal Count", "EventTime": "Occurrence Time", "Details": "Details", "ViewDetails": "View", "EventDetails": "Event Details", "BasicInfo": "Basic Information", "Type": "Type", "Level": "Level", "OccurrenceTime": "Occurrence Time", "EventDescription": "Event Description", "DeviceInfo": "Device Information", "DeviceName": "Device Name", "Status": "Status", "SoftwareVersion": "Software Version", "IPAddress": "IP Address", "MACAddress": "MAC Address", "MaintenanceSuggestions": "Maintenance Suggestions", "MaintenanceDesc": "Regularly monitor terminal access data and network load; mark as resolved when normal, no additional action required", "Cancel": "Cancel", "MarkAsResolved": "<PERSON> as Resolved", "TotalEvents": "Total Events", "SevereEvents": "Severe Events", "NormalEvents": "Normal Events", "MinorEvents": "Minor Events", "NotificationEvents": "Notification Events", "Severe": "Severe", "Normal": "Normal", "Minor": "Minor", "Notification": "Notification", "DevicesWithMoreEvents": "Devices with More Events", "TerminalsWithMoreEvents": "Terminals with More Events", "Online": "Online", "Offline": "Offline", "DeviceTypes": {"AccessType": "Device Access Type", "PerformanceType": "Device Performance Type"}, "EventNames": {"APOnline": "AP Online", "APOffline": "AP Offline", "APHighTemp": "AP High Temperature"}}, "NetworkEventDetail": {"APOnline": "AP Online", "APOffline": "AP Offline", "APHighTemp": "AP High Temperature", "Type": "Type", "DeviceAccess": "Device Access", "DevicePerformance": "<PERSON>ce Performance", "Level": "Level", "Count": "Count", "AffectedDeviceCount": "Affected Devices", "AffectedTerminalCount": "Affected Terminals", "LastTriggerTime": "Last Trigger Time", "PendingEventCount": "Pending Events", "NetworkEventCount": "Network Event Count", "DeviceName": "Device Name", "Model": "Model", "Status": "Status", "EventStatus": "Event Status", "Online": "Online", "Offline": "Offline", "Version": "Version", "IP": "IP Address", "MAC": "MAC Address", "BasicInfo": "Basic Info", "EventDescription": "Event Description", "Maintenance": "Maintenance Suggestions", "MaintenanceDesc": "Regularly count terminal access data and monitor network load; mark as resolved when normal, no extra action needed.", "MarkAsResolved": "<PERSON> as Resolved", "Ignore": "Ignore", "Detail": "Detail", "EventDetails": "Event Details", "Cancel": "Cancel", "Processed": "Processed", "Ignored": "Ignored", "Pending": "Pending"}, "NetworkDeviceDetail": {"TerminalInfo": "Terminal Info", "Status": "Status", "Online": "Online", "Offline": "Offline", "Model": "Model", "Version": "Version", "IPAddress": "IP Address", "MACAddress": "MAC Address", "Uptime": "Uptime", "Runtime": "Runtime", "CPUTitle": "CPU Usage", "CPUSubtitle": "Real-time CPU Load Monitoring", "CPUAvg": "Average CPU Usage", "MemoryTitle": "Memory Usage", "MemorySubtitle": "Real-time Memory Usage Monitoring", "MemoryAvg": "Average Memory Usage", "EventList": "Event List", "NoMoreRemind": "No More Reminders", "EventLevel": "Event Level", "EventType": "Event Type", "EventName": "Event Name", "OccurrenceTime": "Occurrence Time", "EventStatus": "Event Status", "Action": "Action", "DeviceAccess": "Device Access", "DevicePerformance": "<PERSON>ce Performance", "APOnline": "AP Online", "APOffline": "AP Offline", "APHighTemp": "AP High Temperature", "Pending": "Pending", "Ignored": "Ignored", "Processed": "Processed", "MarkAsResolved": "<PERSON> as Resolved", "Ignore": "Ignore", "Detail": "Detail", "EventDetails": "Event Details", "BasicInfo": "Basic Info", "EventDescription": "Event Description", "DeviceInfo": "Device Info", "Remove": "Remove", "Maintenance": "Maintenance Suggestions", "MaintenanceDesc": "Regularly count terminal access data and monitor network load; mark as resolved when normal, no extra action needed.", "Cancel": "Cancel", "NO": "Index", "ConfirmRemove": "Confirm Remove", "Tip": "Tip", "Confirm": "Confirm", "NoMoreRemindEvents": "No More Remind Events", "Type": "Type", "Level": "Level"}, "GetDeviceCPUStatisticsFailed": "Get Device CPU Statistics Failed", "GetDeviceMemoryStatisticsFailed": "Get Device Memory Statistics Failed", "ApiErrors": {"PleaseLogin": "Please login first", "SetGetFailed": "Set/Get failed", "JSONFormatError": "JSON format error", "UnknownRequestType": "Unknown request type", "LoginFailed": "<PERSON><PERSON> failed", "InvalidValue": "Invalid value", "InsufficientSpace": "Insufficient space", "GetInfoFailed": "Failed to get information", "NotLoggedIn": "Not logged in", "ImportConfigError": "Import configuration file error", "ExportConfigError": "Export configuration file error", "SystemBusyOtherRequest": "System busy, running other requests", "ProcessingOtherRequest": "Processing other requests", "NetworkRestart": "Network restart", "SystemRestart": "System restart", "UsernamePasswordError": "Username or password error", "FileError": "File error", "RelaySettingError": "Relay setting error", "SamePassword": "New password is the same as original password", "NetworkError": "Network Error"}, "TimeOut": "超时"}