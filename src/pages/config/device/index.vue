<script lang="ts" setup>
import { useI18n } from 'vue-i18n'

const page = ref(1)
const route = useRoute()
const { t } = useI18n()

const headers = [
  { title: t('Config.Device.DeviceName'), key: 'user_name', sortable: false },
  { title: t('Config.Device.DeviceType'), key: 'type', sortable: false },
  { title: t('Config.Device.DeviceModel'), key: 'model', sortable: false },
  { title: t('Config.Device.OnlineStatus'), key: 'onoff', sortable: false },
  { title: t('Config.Device.Actions'), key: 'actions', sortable: false },
]

let tpl_id = ''
onMounted(() => {
  console.log('route', route)
  route.query.tpl_id && (tpl_id = route.query.tpl_id as string)
  console.log('tpl_id', tpl_id)
  if (tpl_id) {
    getTempleInfo()
    getTempleDeviceStatistic()
    getLinkedDeivceList()
  }
})

const router = useRouter()

const toAP = (item: any) => {
  router.push({
    name: 'network-device-detail',
    query: {
      mac: item.mac,
    },
  })
}

interface DeviceStatistic {
  sum: number
  online: number
  offline: number
}

const deviceStatistic = ref({
  sum: 0,
  online: 0,
  offline: 0,
} as DeviceStatistic)

const modeInfo = ref({})

function getTempleInfo() {
  $api('', {
    requestType: 517,
    data: {
      id: tpl_id,
    },
  }).then(res => {
    if (res.err_code === 0)
      modeInfo.value = res.info
  })
}

function getTempleDeviceStatistic() {
  $api('', {
    requestType: 534,
    data: {
      id: tpl_id,
    },
  }).then(res => {
    if (res.err_code === 0)
      deviceStatistic.value = res.info
  })
}

interface Device {
  sn: string
  model: string
  mac: string
  uptime: string
  onoff: string
  net_type: string
  wifi2g: string
  wifi5g: string
  version: string
  ip: string
  blink: string
  psw: string
  internet: string
  num: string
  runtime: string
  user_name: string
  user_group: string

}

const deviceList = ref([] as Device[])

function getLinkedDeivceList() {
  $api('', {
    requestType: 533,
    data: {
      id: tpl_id,
    },
  }).then(res => {
    if (res.err_code === 0) {
      const list = res.info.ap

      // 循环设置类型type  为AP
      list.forEach(item => {
        item.type = 'AP'
      })
      deviceList.value = list
    }
  })
}
</script>

<template>
  <div>
    <VCard>
      <VCardTitle class="px-6 pt-6">
        <h1 class="text-h5">
          {{ modeInfo.tpl_name }}
        </h1>
      </VCardTitle>
      <VRow class="pa-4">
        <VCol cols="3">
          <div class="template-statistic pa-2 d-flex flex-column align-center">
            <div>{{ t('Config.Device.DeviceTotal') }}</div>
            <div class="text-h3">
              {{ deviceStatistic.sum }}
            </div>
          </div>
        </VCol>
        <VCol cols="3">
          <div class="template-statistic pa-2 d-flex flex-column align-center online">
            <div>{{ t('Config.Device.Online') }}</div>
            <div class="text-h3 success">
              {{ deviceStatistic.online }}
            </div>
          </div>
        </VCol>
        <VCol cols="3">
          <div class="template-statistic pa-2 d-flex flex-column align-center offline">
            <div>{{ t('Config.Device.Offline') }}</div>
            <div class="text-h3 error">
              {{ deviceStatistic.offline }}
            </div>
          </div>
        </VCol>
        <VCol cols="3">
          <div class="template-statistic pa-2 d-flex flex-column align-center">
            <div>{{ t('Config.Device.DeviceModel') }}</div>
            <div class="text-h3">
              AP310i
            </div>
          </div>
        </VCol>
      </VRow>
    </VCard>
    <VCard class="mt-8">
      <VCardTitle class="pa-6">
        <h1 class="text-h5">
          {{ t('Config.Device.DeviceList') }}
        </h1>
      </VCardTitle>
      <VDivider />
      <VDataTableServer
        :headers="headers"
        :items="deviceList"
        :items-length="10"
        :page="page"
        show-select
        :no-data-text="t('NoData')"
      >
        <template #item.onoff="{ item }">
          <div>
            <VChip
              :color="item.onoff === 'online' ? 'success' : 'error'"
              text-color="white"
            >
              {{ item.onoff === 'online' ? t('Config.Device.Online') : t('Config.Device.Offline') }}
            </VChip>
          </div>
        </template>
        <template #item.actions="{ item }">
          <VBtn
            color="primary"
            variant="text"
            @click="toAP(item)"
          >
            {{ t('Config.Device.DeviceDetails') }}
          </VBtn>
        </template>
        <template #bottom>
          <TablePagination
            v-model:page="page"
            :items-per-page="10"
            :total-items="10"
          />
        </template>
      </VDataTableServer>
    </VCard>
  </div>
</template>

<style lang="scss" scoped>
.template-statistic {
  border-radius: 6px;
  background: rgba($color: var(--v-theme-secondary), $alpha: 8%);
  block-size: 76px;
  color: rgb(var(--v-theme-on-surface));
  font-size: 15px;
  inline-size: 100%;

  &.online {
    background: rgba($color: var(--v-theme-success), $alpha: 8%);
  }

  &.offline {
    background: rgba($color: var(--v-theme-error), $alpha: 8%);
  }

  .success {
    color: rgb(var(--v-theme-success));
  }

  .error {
    color: rgb(var(--v-theme-error));
  }
}
</style>
