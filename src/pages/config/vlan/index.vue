<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { onMounted, ref, watch } from 'vue'
import { isByteLengthInRange } from '@layouts/utils'

// VLAN数据接口
interface VlanConfig {
  id: number
  enable: boolean
  vlanId: number
  name: string
  vlanIp: string
  subnetMask: string
  gateway: string
  dhcpEnabled: boolean
  dhcpStartAddress: string
  dhcpEndAddress: string
  remark: string
  status: 'active' | 'inactive'
}

// 响应式数据
const loading = ref(false)
const selectedVlans = ref<number[]>([])
const isAddDrawerOpen = ref(false)
const isEditMode = ref(false)
const currentVlan = ref<VlanConfig | null>(null)

// 分页状态
const itemsPerPage = ref(10)
const page = ref(1)

// 目前不支持
// { title: 'VLAN IP/子网掩码', key: 'vlanIp', sortable: false },
// { title: 'DHCP地址池', key: 'dhcpPool', sortable: false },

// 表头配置
const headers = [
  { title: 'VLAN ID', key: 'vlanId', sortable: true },
  { title: '名称', key: 'name', sortable: true },

  { title: '启用状态', key: 'enable', sortable: false },
  { title: '备注', key: 'remark', sortable: false },
  { title: '操作', key: 'actions', sortable: false },
]

// VLAN列表数据和总数
const vlanList = ref<VlanConfig[]>([])
const totalVlans = ref(0)

// 排序相关变量
const sortBy = ref<{ key: string; order?: 'asc' | 'desc' }[]>([])

// 获取VLAN列表
const getVlanList = () => {
  loading.value = true
  $get('/v1/vlanConfigs', { page: page.value, size: itemsPerPage.value }).then((res: any) => {
    if (res.msg === 'success' || res.msg === 'success') {
      // 兼容不同后端返回结构
      vlanList.value = res.result?.rows || []
      totalVlans.value = res.result?.count || 0
    }
    else {
      ElMessage.error(res.err_message || res.msg || '获取VLAN列表失败')
    }
  }).finally(() => {
    loading.value = false
  })
}

// 分页变化时自动获取
watch([page, itemsPerPage], getVlanList, { immediate: true })

// 排序事件（如需后端排序可扩展）
const sortchange = (val: any) => {
  sortBy.value = val

  // 可扩展：带排序参数请求后端
}

// 表单数据
const formData = ref({
  enable: true,
  vlanId: '',
  name: '',
  remark: '', // 备注字段移出DHCP控制
})

// 表单校验规则
const nameRules = [
  (v: string) => (!!v && v.trim() !== '') || '名称不能为空',
  (v: string) => (isByteLengthInRange(v, 1, 64)) || '名称长度为1-32字节',
]

const vlanIdRules = [
  (v: string | number) => (!!v) || 'VLAN ID 必填',
  (v: string | number) => {
    const num = Number(v)

    return (Number.isInteger(num) && num >= 1 && num <= 4094) || 'VLAN ID 必须为1-4094的整数'
  },
  (v: string | number) => {
    const num = Number(v)

    // 编辑模式下，如果是当前VLAN的ID，则允许
    if (isEditMode.value && currentVlan.value && currentVlan.value.vlanId === num)
      return true

    // 检查是否已被其他VLAN使用
    const isUsed = vlanList.value.some(vlan => vlan.vlanId === num)

    return !isUsed || 'VLAN ID 已被使用，请选择其他值'
  },
]

const formRef = ref()

// 打开新建VLAN抽屉
const openAddDrawer = () => {
  isEditMode.value = false
  currentVlan.value = null
  formData.value = {
    enable: true,
    vlanId: '',
    name: '',
    remark: '',
  }
  isAddDrawerOpen.value = true
}

// 关闭VLAN抽屉
const closeDrawer = () => {
  isAddDrawerOpen.value = false
  isEditMode.value = false
  currentVlan.value = null
}

// 编辑VLAN
const editVlan = (vlan: VlanConfig) => {
  isEditMode.value = true
  currentVlan.value = vlan
  formData.value = {
    enable: vlan.enable,
    vlanId: vlan.vlanId.toString(),
    name: vlan.name,
    remark: vlan.remark || '',
  }
  isAddDrawerOpen.value = true
}

// 自定义确认对话框状态
const deleteDialogVisible = ref(false)

const deleteDialogData = ref({
  vlan: null as VlanConfig | null,
  message: '',
  title: '确认删除VLAN配置',
  isUsed: false,
  usageCount: 0,
  countdown: 0,
  canConfirm: true,
})

// 倒计时定时器
let countdownTimer: NodeJS.Timeout | null = null

// 删除VLAN
const deleteVlan = async (vlan: VlanConfig) => {
  try {
    // 先检查VLAN是否被使用
    // const checkRes = await $get(`/v1/vlanConfig/${vlan.id}/usage`)
    const checkRes = {
      msg: 'success',
    }

    let confirmMessage = ''
    let isUsed = false
    let usageCount = 0

    if (checkRes.msg === 'success') {
      // 根据返回结果判断是否被使用
      isUsed = checkRes.result?.isUsed || true
      usageCount = checkRes.result?.usageCount || 0

      if (isUsed) {
        // 右边的对话框 - VLAN被使用，需要倒计时
        confirmMessage = `当前VLAN配置已应用到 ${usageCount} 个模板，请谨慎操作。\n是否确认删除VLAN配置 ${vlan.name}，VLAN ID ${vlan.vlanId}。`
      }
    }
    else {
      // 检查接口失败，使用默认提示
      confirmMessage = `确定要删除该VLAN吗？${checkRes.msg}`
    }

    // 设置对话框数据
    deleteDialogData.value = {
      vlan,
      message: confirmMessage,
      title: '确认删除VLAN配置',
      isUsed,
      usageCount,
      countdown: isUsed ? 3 : 0,
      canConfirm: !isUsed,
    }

    // 显示对话框
    deleteDialogVisible.value = true

    // 如果被使用，启动倒计时
    if (isUsed)
      startCountdown()
  }
  catch (error) {
    ElMessage.error('检查VLAN使用状态失败')
  }
}

// 启动倒计时
const startCountdown = () => {
  if (countdownTimer)
    clearInterval(countdownTimer)

  countdownTimer = setInterval(() => {
    if (deleteDialogData.value.countdown > 0) {
      deleteDialogData.value.countdown--
    }
    else {
      deleteDialogData.value.canConfirm = true
      if (countdownTimer) {
        clearInterval(countdownTimer)
        countdownTimer = null
      }
    }
  }, 1000)
}

// 确认删除
const confirmDelete = async () => {
  if (!deleteDialogData.value.canConfirm || !deleteDialogData.value.vlan)
    return

  deleteDialogVisible.value = false
  loading.value = true

  try {
    const res = await $delete(`/v1/vlanConfig/${deleteDialogData.value.vlan.id}`)
    if (res.msg === 'success' || res.msg === 'success') {
      ElMessage.success('删除成功')
      getVlanList() // 重新获取列表
    }
    else {
      ElMessage.error(res.err_message || res.msg || '删除失败')
    }
  }
  catch (error) {
    ElMessage.error('删除失败')
  }
  finally {
    loading.value = false
  }
}

// 取消删除
const cancelDelete = () => {
  deleteDialogVisible.value = false
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
}

// 保存VLAN
const saveVlan = async () => {
  const valid = await formRef.value?.validate?.()
  if (!valid)
    return

  const payload = {
    name: formData.value.name,
    enable: formData.value.enable,
    vlanId: Number(formData.value.vlanId),
    remark: formData.value.remark,
  }

  loading.value = true
  try {
    let res
    if (isEditMode.value && currentVlan.value)
      res = await $put(`/v1/vlanConfig/${currentVlan.value.id}`, payload)
    else
      res = await $post('/v1/vlanConfig', payload)

    if (res.msg === 'success' || res.msg === 'success') {
      ElMessage.success('保存成功')
      isAddDrawerOpen.value = false
      getVlanList()
    }
    else {
      ElMessage.error(res.err_message || res.msg || '保存失败')
    }
  }
  finally {
    loading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  getVlanList()
})

// 组件销毁时清理定时器
onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
})
</script>

<template>
  <div class="vlan-config-page">
    <!-- 信息提示区域 -->
    <div class="d-flex align-start mt-2 bg-primary-transparent pa-3 rounded border border-primary">
      <VIcon
        icon="tabler-alert-circle"
        class="me-6"
      />
      <ol class="text-primary text-subtitle-1">
        <li>AP 必须连接到 AC（直接连接或通过交换机 Trunk 连接）。</li>
        <li>交换机必须支持 VLAN 报文转发：VLAN 交换机连接 AC 时需配置 Trunk 端口。</li>
        <li>VLAN 网段必须与 AC/路由器的 LAN 和 PPPoE 网段区分开。</li>
      </ol>
    </div>

    <!-- VLAN列表 -->
    <VCard class="vlan-table-card mt-6">
      <!-- 页面标题和操作按钮 -->
      <div class="d-flex flex-wrap gap-4 ma-6">
        <h2 class="page-title">
          VLAN列表
        </h2>
        <VSpacer />
        <VBtn
          color="primary"
          @click="openAddDrawer"
        >
          + 新建VLAN
        </VBtn>
      </div>
      <VDivider />

      <VDataTable
        v-model="selectedVlans"
        v-model:items-per-page="itemsPerPage"
        v-model:page="page"
        :headers="headers"
        :items="vlanList"
        :loading="loading"
        show-select
        item-value="id"
        class="text-no-wrap"
        no-data-text="暂无数据"
        :sort-by="sortBy"
        @update:sort-by="sortchange"
      >
        <!-- VLAN IP/子网掩码列 -->
        <template #item.vlanIp="{ item }">
          {{ item.vlanIp || '--' }}/{{ item.subnetMask || '--' }}
        </template>

        <!-- DHCP地址池列 -->
        <template #item.dhcpPool="{ item }">
          <span v-if="item.dhcpEnabled">
            {{ item.dhcpStartAddress }} - {{ item.dhcpEndAddress }}
          </span>
          <span
            v-else
            class="text-disabled"
          >
            未启用
          </span>
        </template>

        <!-- 备注列 -->
        <template #item.remark="{ item }">
          <span class="text-disabled">
            {{ item.remark || '--' }}
          </span>
        </template>
        <!-- 启用状态列 -->
        <template #item.enable="{ item }">
          <VChip
            :color="item.enable ? 'success' : 'error'"
            label
            size="small"
          >
            {{ item.enable ? '启用' : '禁用' }}
          </VChip>
        </template>

        <!-- 操作列 -->
        <template #item.actions="{ item }">
          <div class="d-flex gap-2">
            <VBtn
              color="primary"
              size="small"
              variant="text"
              @click="editVlan(item)"
            >
              编辑
            </VBtn>
            <VBtn
              color="error"
              size="small"
              variant="text"
              @click="deleteVlan(item)"
            >
              删除
            </VBtn>
          </div>
        </template>

        <!-- 底部分页 -->
        <template #bottom>
          <TablePagination
            v-model:page="page"
            :items-per-page="itemsPerPage"
            :total-items="totalVlans"
          />
        </template>
      </VDataTable>
    </VCard>

    <!-- 删除确认对话框 -->
    <VDialog
      v-model="deleteDialogVisible"
      max-width="500"
      persistent
    >
      <VCard>
        <VCardTitle class="text-h6">
          {{ deleteDialogData.title }}
        </VCardTitle>
        <VCardText>
          <div
            class="text-body-1 mb-4"
            style="white-space: pre-line;"
          >
            {{ deleteDialogData.message }}
          </div>
        </VCardText>
        <VCardActions class="justify-end">
          <VBtn
            color="secondary"
            variant="text"
            @click="cancelDelete"
          >
            取消
          </VBtn>
          <VBtn
            color="error"
            :disabled="!deleteDialogData.canConfirm"
            @click="confirmDelete"
          >
            <template v-if="deleteDialogData.isUsed && deleteDialogData.countdown > 0">
              确认删除 ({{ deleteDialogData.countdown }})
            </template>
            <template v-else>
              确认删除
            </template>
          </VBtn>
        </VCardActions>
      </VCard>
    </VDialog>

    <!-- 右侧VLAN配置抽屉 -->
    <VNavigationDrawer
      v-if="isAddDrawerOpen"
      v-model="isAddDrawerOpen"
      location="end"
      temporary
      width="600"
      class="add-vlan-drawer"
    >
      <div class="h-screen d-flex flex-column">
        <!-- 顶部固定区域 -->
        <div class="flex-shrink-0 d-flex align-center justify-space-between pa-4">
          <div class="text-h5">
            {{ isEditMode ? '编辑VLAN' : '新建VLAN' }}
          </div>
          <VBtn
            color="medium-emphasis"
            icon
            size="small"
            variant="text"
            @click="closeDrawer"
          >
            <VIcon
              color="high-emphasis"
              icon="tabler-x"
              size="24"
            />
          </VBtn>
        </div>

        <VDivider />

        <!-- 中间可滚动区域 -->
        <div class="flex-grow-1 overflow-hidden">
          <PerfectScrollbar
            :options="{ wheelPropagation: false }"
            class="h-100"
            tag="div"
          >
            <div class="pa-4 d-flex flex-column h-100">
              <VForm
                ref="formRef"
                class="vlan-form"
              >
                <!-- 启用VLAN -->
                <div class="d-flex align-start mb-4">
                  <div class="w-80 h-38 flex-0-0 me-4 text-subtitle-2">
                    启用VLAN
                  </div>
                  <div class="w-100">
                    <div class="d-flex align-center justify-end">
                      <VSwitch
                        v-model="formData.enable"
                        class="me-2"
                      />
                      <span class="text-subtitle-2">{{ formData.enable ? '启用' : '禁用' }}</span>
                    </div>
                  </div>
                </div>
                <!-- 名称 -->
                <div class="d-flex align-start mb-4">
                  <div class="w-80 h-38 flex-0-0 me-4 text-subtitle-2">
                    名称
                  </div>
                  <div class="w-100">
                    <AppTextField
                      v-model="formData.name"
                      placeholder="请输入名称"
                      append-inner-icon="tabler-edit"
                      :rules="nameRules"
                    />
                  </div>
                </div>
                <!-- VLAN ID -->
                <div class="d-flex align-start mb-4">
                  <div class="w-80 h-38 flex-0-0 me-4 text-subtitle-2">
                    VLAN ID
                  </div>
                  <div class="w-100">
                    <AppTextField
                      v-model="formData.vlanId"
                      placeholder="请输入VLAN ID（1-4094）"
                      type="number"
                      append-inner-icon="tabler-edit"
                      :rules="vlanIdRules"
                    />
                  </div>
                </div>

                <!-- 备注 -->
                <div class="d-flex align-start mb-4">
                  <div class="w-80 h-38 flex-0-0 me-4 text-subtitle-2">
                    备注（可选）
                  </div>
                  <div class="w-100">
                    <AppTextField
                      v-model="formData.remark"
                      placeholder="请输入备注信息"
                      append-inner-icon="tabler-edit"
                    />
                  </div>
                </div>
              </VForm>
            </div>
          </PerfectScrollbar>
        </div>

        <!-- 底部固定区域 -->
        <VDivider />
        <div class="flex-shrink-0 pa-4 d-flex align-center justify-space-between">
          <div />
          <div>
            <VBtn
              class="me-4"
              color="secondary"
              variant="tonal"
              @click="closeDrawer"
            >
              取消
            </VBtn>
            <VBtn
              color="primary"
              @click="saveVlan"
            >
              保存
            </VBtn>
          </div>
        </div>
      </div>
    </VNavigationDrawer>
  </div>
</template>

<style scoped lang="scss">
.vlan-config-page {
  block-size: 100%;
}

.info-content {
  .info-item {
    line-height: 1.5;
    margin-block-end: 8px;

    &:last-child {
      margin-block-end: 0;
    }
  }
}

.vlan-table-card {
  .table-pagination {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;

    .pagination-controls {
      margin: 0;
    }
  }
}

// AP-style form classes for modal dialog
.vlan-form {
  .h-38 {
    block-size: 38px;
    line-height: 38px;
  }
}

.w-80 {
  inline-size: 80px;
}
</style>
