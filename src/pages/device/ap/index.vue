<script lang="ts" setup>
import type { UploadFile, UploadFiles, UploadRawFile } from 'element-plus'
import { ElMessage, ElMessageBox, ElUpload, genFileId } from 'element-plus'
import { computed, nextTick, reactive, ref, watch, watchEffect } from 'vue'
import { useI18n } from 'vue-i18n'
import {
  BAND_WIDTH_2G,
  COUNTRY_OPTIONS,
  NET_TYPE,
  PROTOCOL_2G,
  TX_POWER_2G,
} from '@/utils/constants'

const { t } = useI18n()
const selectedRows = ref([])

const itemsPerPage = ref(10)
const page = ref(1)

// 表头
const headers = [
  { title: t('Device.AP.Name'), key: 'user_name', sortable: false },
  { title: t('Device.AP.Model'), key: 'model', sortable: false },
  { title: t('Device.AP.SerialNumber'), key: 'sn', sortable: true },
  { title: t('Device.AP.IPAddress'), key: 'ip', sortable: false },
  { title: t('Device.AP.MACAddress'), key: 'mac', sortable: false },
  { title: t('Device.AP.FirmwareVersion'), key: 'version', sortable: false },
  { title: t('Device.AP.RunningTime'), key: 'uptime', sortable: false },
  { title: t('Device.AP.Status'), key: 'onoff', sortable: false },
  { title: t('Device.AP.Actions'), key: 'actions', sortable: false },
]

// 子网掩码选项列表
const subnetMaskOptions = [
  // A类网络常用
  { label: '*********/8', value: '*********' },
  { label: '***********/9', value: '***********' },
  { label: '***********/10', value: '***********' },
  { label: '***********/11', value: '***********' },
  { label: '***********/12', value: '***********' },
  { label: '***********/13', value: '***********' },
  { label: '***********/14', value: '***********' },
  { label: '***********/15', value: '***********' },

  // B类网络常用
  { label: '***********/16', value: '***********' },
  { label: '*************/17', value: '*************' },
  { label: '*************/18', value: '*************' },
  { label: '255.255.224.0/19', value: '255.255.224.0' },
  { label: '255.255.240.0/20', value: '255.255.240.0' },
  { label: '255.255.248.0/21', value: '255.255.248.0' },
  { label: '255.255.252.0/22', value: '255.255.252.0' },
  { label: '255.255.254.0/23', value: '255.255.254.0' },

  // C类网络常用
  { label: '255.255.255.0/24', value: '255.255.255.0' },
  { label: '255.255.255.128/25', value: '255.255.255.128' },
  { label: '255.255.255.192/26', value: '255.255.255.192' },
  { label: '255.255.255.224/27', value: '255.255.255.224' },
  { label: '255.255.255.240/28', value: '255.255.255.240' },
  { label: '255.255.255.248/29', value: '255.255.255.248' },
  { label: '255.255.255.252/30', value: '255.255.255.252' },
]

const operation = ref()

// No longer reset selections when operation changes
const operationChange = () => {
  // Keep selections intact
}

const apHandlers = ref([{
  label: t('Device.AP.Operations.Restart'),
  value: 0,
}, {
  label: t('Device.AP.Operations.FactoryReset'),
  value: 1,
}, {
  label: t('Device.AP.Operations.Upgrade'),
  value: 2,
}, {
  label: t('Device.AP.Operations.Delete'),
  value: 3,
}, {
  label: t('Device.AP.Operations.BlinkLED'),
  value: 4,
}, {
  label: t('Device.AP.Operations.ExportSN'),
  value: 5,
}])

// 数据
const apData = ref({
  total: 0,
  apList: [],
})

const loading = ref(false)
const selectedModel = ref()
const modelList = ref([] as { label: string; value?: string }[])

const ledMode = ref('1')

const resetAfterUpdate = ref('0')

const uploader = ref()
const fileList = ref()

const handleChange = (uploadFile: UploadFile) => {
  fileList.value = [uploadFile]
}

const handleExceed = (files: UploadFiles) => {
  uploader.value!.clearFiles()

  const file = files[0] as UploadRawFile

  file.uid = genFileId()
  uploader.value!.handleStart(file)
}

// 排序相关变量
const sortBy = ref<{ key: string; order?: string }[]>([])

// 修改apList，支持本地排序
const apList = computed((): any[] => {
  let list = apData.value.apList

  if (selectedModel.value)
    list = list.filter((item: any) => item.model === selectedModel.value)

  // 本地排序
  if (sortBy.value.length > 0) {
    const { key, order } = sortBy.value[0]

    list = [...list].sort((a, b) => {
      const aValue = a[key]
      const bValue = b[key]
      if (aValue < bValue)
        return order === 'asc' ? -1 : 1
      if (aValue > bValue)
        return order === 'asc' ? 1 : -1

      return 0
    })
  }

  return list
})

// We don't need to watch apList changes and filter selections
// This allows users to maintain their selection state when changing operations

const totalProduct = computed(() => {
  if (selectedModel.value)
    return apData.value.apList.filter((item: any) => item.model === selectedModel.value).length || 0

  return apData.value.apList.length || 0
})

// 扫描
const activeAP: any = ref({})

const router = useRouter()

const remoteConfigFun = (item: any) => {
  if (item.net_type == 0) {
    // 打开新页面
    const url = `http://${item.ip}`
    const windowName = '_blank'

    window.open(url, windowName)
  }
  else {
    router.push({ name: 'device-remote', query: { mac: item.mac, sn: item.sn, mode: item.net_type } })
  }
}

const deviceManagerFun = (item: any) => {
  console.log('deviceManagerFun', item)

  $api('', {
    requestType: 506,
  }).then(res => {
    if (res.err_code === 0)
      dealData(item, res.info.aplist)
  })
}

// 2.4G
const wirelessFormItems: any = ref([])

// 5G
const wirelessFormItems5G: any = ref([])

// 5.8G 备用
const wirelessFormItems8G: any = ref([])

console.log(wirelessFormItems5G.value, wirelessFormItems8G.value)

const workModeFormItems: any = ref([])

const nameModeFormItems: any = ref([])

// 监听国家码变化并更新信道列表
const channelList = computed(() => {
  if (!wirelessFormItems.value?.[6]?.value)
    return []

  // 根据国家码获取对应的信道列表
  const countryCode = wirelessFormItems.value[6]?.value
  const index = COUNTRY_OPTIONS.findIndex((data: any) => data.value === countryCode)
  const result = CHANNEL_ARR_2G[index] || []

  return result.map(item => ({
    label: item,
    value: item,
  }))
})

const channelList5G = computed(() => {
  if (!wirelessFormItems8G.value?.[6]?.value)
    return []

  // 根据国家码获取对应的信道列表
  const countryCode = wirelessFormItems8G.value[6]?.value
  const index = COUNTRY_OPTIONS.findIndex((data: any) => data.value === countryCode)
  const result = CHANNEL_ARR_5G[index] || []

  return result.map(item => ({
    label: item,
    value: item,
  }))
})

const isFirstUpdate = ref(true)
const isFirstUpdate8G = ref(true)

onMounted(() => {
  watchEffect(() => {
    if (wirelessFormItems.value?.[6]) {
      // 创建一个新的对象来触发响应式更新
      const updatedItem = reactive({
        ...wirelessFormItems.value[7],

        // 只在当前没有有效值或不在新列表中时才设为auto
        ...(!wirelessFormItems.value[7].value
           || !channelList.value.some(item => item.value === wirelessFormItems.value[7].value)
          ? { value: 'auto' }
          : {}),
        list: channelList.value,
      })

      // 确保信道列表正确更新
      wirelessFormItems.value[7] = updatedItem
      isFirstUpdate.value = false
    }
  })
  watchEffect(() => {
    if (wirelessFormItems8G.value?.[6]) {
      // 创建一个新的对象来触发响应式更新
      const updatedItem = reactive({
        ...wirelessFormItems8G.value[7],

        // 只在当前没有有效值或不在新列表中时才设为auto
        ...(!wirelessFormItems8G.value[7].value
           || !channelList5G.value.some(item => item.value === wirelessFormItems8G.value[7].value)
          ? { value: 'auto' }
          : {}),
        list: channelList5G.value,
      })

      // 确保信道列表正确更新
      wirelessFormItems8G.value[7] = updatedItem
      isFirstUpdate8G.value = false
    }
  })
})

const COUNTRY_OPTIONS_LOCALIZED = computed(() => {
  return COUNTRY_OPTIONS.map(item => ({
    ...item,
    label: item.label, // 无需翻译，因为COUNTRY_OPTIONS已经是国家代码
  }))
})

const NET_TYPE_LOCALIZED = computed(() => {
  return NET_TYPE.map(item => ({
    ...item,
    label: t(item.label),
  }))
})

const TX_POWER_2G_LOCALIZED = computed(() => {
  return TX_POWER_2G.map(item => ({
    ...item,
    label: t(item.label),
  }))
})

const TX_POWER_5G_LOCALIZED = computed(() => {
  return TX_POWER_5G.map(item => ({
    ...item,
    label: t(item.label),
  }))
})

const WIFI_ENCRYPTION_TYPE_LOCALIZED = computed(() => {
  return ENCRYPTION_TYPE.map(item => ({
    ...item,
    label: item.label, // 无需翻译，因为WIFI_ENCRYPTION_TYPE已经是技术名称
  }))
})

const PROTOCOL_2G_LOCALIZED = computed(() => {
  return PROTOCOL_2G.map(item => ({
    ...item,
    label: item.label, // 无需翻译，因为PROTOCOL_2G已经是技术标准名称
  }))
})

const BAND_WIDTH_2G_LOCALIZED = computed(() => {
  return BAND_WIDTH_2G.map(item => ({
    ...item,
    label: item.label, // 无需翻译，因为BAND_WIDTH_2G已经是技术参数名称
  }))
})

// IP地址验证器 - 验证最后一位在1-254之间
const validateLanIp = (value: string) => {
  if (!value)
    return true // 允许空值，如果需要必填可以在另一个验证器中处理

  // IP地址格式验证
  const ipRegex = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/
  const match = value.match(ipRegex)

  if (!match)
    return t('Device.AP.InvalidIPFormat')

  // 检查每个部分是否在0-255之间
  const parts = []
  for (let i = 1; i <= 4; i++) {
    const part = Number.parseInt(match[i], 10)
    if (part < 0 || part > 255)
      return t('Device.AP.IPRangeError')
    parts.push(part)
  }

  // 验证是否为私有网络IP地址范围 (RFC 1918)
  const [p1, p2, , p4] = parts
  const isPrivateA = p1 === 10 // 10.0.0.0 - **************
  const isPrivateB = p1 === 172 && (p2 >= 16 && p2 <= 31) // ********** - **************
  const isPrivateC = p1 === 192 && p2 === 168 // *********** - ***************

  if (!(isPrivateA || isPrivateB || isPrivateC))
    return t('Device.AP.NotPrivateIP')

  // 验证最后一位不能为0或255（网络地址和广播地址）
  if (p4 === 0 || p4 === 255)
    return t('Device.AP.IPLastByteError')

  return true
}

// 其他验证器
const requiredValidator = (value: string) => !!value || t('Device.AP.Required')

const dealData = (info: any, list: any[]) => {
  // 获取list中sn 的数据跟info一样的覆盖生成item
  console.log('list', list)
  let item = list.find((data: any) => data.sn === info.sn)
  if (!item) {
    item = info
  }
  else {
    item = {
      ...info,
      ...item,
    }
  }
  activeAP.value = item
  deviceInfoTable.value = [
    {
      label: t('Device.AP.DeviceTypeDesc'),
      value: 'AP',
    },
    {
      label: t('Device.AP.DeviceType'),
      value: item.model,
    },
    {
      label: t('Device.AP.SerialNumber'),
      value: item.sn,
    },
    {
      label: t('Device.AP.IPAddress'),
      value: item.ip,
    },
    {
      label: t('Device.AP.MACAddress'),
      value: item.mac,
    },
    {
      label: t('Device.AP.FirmwareVersion'),
      value: item.version || t('Device.AP.NoDataYet'),
    },
    {
      label: t('Device.AP.InterfaceRate'),
      value: `${item.speed} Mbps/s`,
    },
  ]
  if (item.wifiHtMode_2G == 'HT40') {
    if (item.wifiForce40MHzMode_2G == '0') {
      console.log('wirelessFormItems', 111)
      selectedBandwidth.value = '20/40M'
      HTMode.value = 'HT40'
    }
    else {
      console.log('wirelessFormItems', 222)
      selectedBandwidth.value = '40M'
      HTMode.value = 'HT40'
    }
  }
  else {
    console.log('wirelessFormItems', 333)
    selectedBandwidth.value = '20M'
    HTMode.value = 'HT20'
  }

  console.log('wirelessFormItems', item)
  wirelessFormItems.value = [
    {
      label: t('Device.AP.SSID'),
      value: item.ssid_2g,
      key: 'ssid',
      formType: 'input',
    },
    {
      label: t('Device.AP.EncryptionType'),
      value: item.encryption_2g,
      list: WIFI_ENCRYPTION_TYPE_LOCALIZED.value,
      key: 'encryption',
      formType: 'select',
    },
    {
      label: t('Device.AP.Password'),
      value: item.key_2g,
      key: 'password',
      formType: 'password',
      rules: [validatePasswordEight],
    },
    {
      label: t('Device.AP.Status'),
      value: item.wifiOnOff_2G === '0',
      key: 'status',
      formType: 'switch',
    },
    {
      label: t('Device.AP.APIsolation'),
      value: item.wifiApIsolate_2G === '1',
      key: 'apIsolation',
      formType: 'switch',
    },
    {
      label: t('Device.AP.Protocol'),
      value: item.wifiHwMode_2G,
      list: PROTOCOL_2G_LOCALIZED.value,
      key: 'protocol',
      formType: 'select',
    },
    {
      label: t('Device.AP.CountryCode'),
      value: item.wifiCountry_2G,
      list: COUNTRY_OPTIONS_LOCALIZED.value,
      key: 'countryCode',
      formType: 'select',
    },
    {
      label: t('Device.AP.Channel'),
      value: item.wifiChannel_2G,
      list: [],
      key: 'channel',
      formType: 'select',
    },
    {
      label: t('Device.AP.BandWidth'),
      value: selectedBandwidth.value,
      list: BAND_WIDTH_2G_LOCALIZED.value,
      key: 'bandwidth',
      formType: 'select',
      onChange: handleBandwidthChange,
    },
    {
      label: t('Device.AP.TransmitPower'),
      value: item.wifiTxpower_2G,
      list: TX_POWER_2G_LOCALIZED.value,
      key: 'transmitPower',
      formType: 'select',
    },
    {
      label: t('Device.AP.MaxConnections'),
      value: item.wifiMaxsta_2G,
      key: 'clientLimit',
      formType: 'input',
    },
  ]
  console.log(wirelessFormItems.value, item, 9999)

  // 初始化2.4G信道列表
  const index2G = COUNTRY_OPTIONS.findIndex((data: any) => data.value === item.wifiCountry_2G)
  if (index2G !== -1) {
    wirelessFormItems.value[7].list = CHANNEL_ARR_2G[index2G].map((channel: string) => ({
      label: channel,
      value: channel,
    }))
  }
  wirelessFormItems8G.value = [
    {
      label: t('Device.AP.SSID'),
      value: item.ssid_5g,
      key: 'ssid',
      formType: 'input',
    },
    {
      label: t('Device.AP.EncryptionType'),
      value: item.encryption_5g,
      list: WIFI_ENCRYPTION_TYPE_LOCALIZED.value,
      key: 'encryption',
      formType: 'select',
    },
    {
      label: t('Device.AP.Password'),
      value: item.key_5g,
      key: 'password',
      formType: 'password',
      rules: [validatePasswordEight],
    },
    {
      label: t('Device.AP.Status'),
      value: item.wifiOnOff_5G === '0',
      key: 'status',
      formType: 'switch',
    },
    {
      label: t('Device.AP.APIsolation'),
      value: item.wifiApIsolate_5G === '1',
      key: 'apIsolation',
      formType: 'switch',
    },
    {
      label: t('Device.AP.Protocol'),
      value: item.wifiHwMode_5G,
      list: PROTOCOL_5G,
      key: 'protocol',
      formType: 'select',
    },
    {
      label: t('Device.AP.CountryCode'),
      value: item.wifiCountry_5G,
      list: COUNTRY_OPTIONS_LOCALIZED.value,
      key: 'countryCode',
      formType: 'select',
    },
    {
      label: t('Device.AP.Channel'),
      value: item.wifiChannel_5G,
      list: [],
      key: 'channel',
      formType: 'select',
      onChange: changeChannel,
    },
    {
      label: t('Device.AP.BandWidth'),
      value: item.wifiHtMode_5G,
      list: BAND_WIDTH_5G_LOCALIZED.value,
      key: 'bandwidth',
      formType: 'select',
    },
    {
      label: t('Device.AP.TransmitPower'),
      value: item.wifiTxpower_5G,
      list: TX_POWER_5G_LOCALIZED,
      key: 'transmitPower',
      formType: 'select',
    },
    {
      label: t('Device.AP.MaxConnections'),
      value: item.wifiMaxsta_5G,
      key: 'clientLimit',
      formType: 'input',
    },
  ]

  // 初始化5G信道列表
  const index5G = COUNTRY_OPTIONS.findIndex((data: any) => data.value === item.wifiCountry_5G)
  if (index5G !== -1) {
    wirelessFormItems8G.value[7].list = CHANNEL_ARR_5G[index5G].map((channel: string) => ({
      label: channel,
      value: channel,
    }))
  }
  nameModeFormItems.value = [
    {
      label: t('Device.AP.DeviceName'),
      value: item.user_name,
      key: 'name',
      formType: 'input',
    },
  ]
  workModeFormItems.value = [
    {
      label: t('Device.AP.WorkingMode'),
      value: item.net_type,
      key: 'workMode',
      formType: 'select',
      list: NET_TYPE_LOCALIZED.value,
      subtitle: `${t('Device.AP.RouterModeAPModeDesc')}
${t('Device.AP.RouterModeDesc')}
${t('Device.AP.APModeDesc')}`,
    },
    {
      label: t('Device.AP.LANIP'),
      value: item.lan_ip,
      key: 'lan',
      formType: 'input',
      rules: [requiredValidator, validateLanIp],
    },
    {
      label: t('NetworkConfig.LAN.SubnetMask'),
      value: item.lan_netmask,
      formType: 'select',
      list: subnetMaskOptions,
      rules: [requiredValidator],
      key: 'subnetMask',
    },
    {
      label: t('Device.AP.LAN1'),
      value: item.eth1_link === 'up' ? t('Device.AP.Connected') : t('Device.AP.Disconnected'),
      key: 'lan1',
      formType: 'text',
    },
    {
      label: t('Device.AP.LAN2'),
      value: item.eth0_link === 'up' ? t('Device.AP.Connected') : t('Device.AP.Disconnected'),
      key: 'lan2',
      formType: 'text',
    },
  ]
  drawer.value = true
}

const restartDeviceFun = (item: any) => {
  if (!item.sn) {
    ElMessage.error(t('Device.AP.NoSN'))

    return
  }

  ElMessageBox.confirm(t('Device.AP.RestartConfirm'), t('Device.AP.Tip'), {
    confirmButtonText: t('Device.AP.Confirm'),
    cancelButtonText: t('Device.AP.Cancel'),
    type: 'warning',
  }).then(() => {
    $api('', {
      requestType: 501,
      data: {
        ap_sn_list: `${item.sn} `,
      },
    }).then(res => {
      if (res.err_code === 0)
        ElMessage.success(t('Device.AP.RestartSuccess'))
      dealRequestData()
    })
  }).catch(() => {
    ElMessage.info(t('Device.AP.RestartCancelled'))
  })
}

const delDeviceFun = (item: any) => {
  if (!item.sn) {
    ElMessage.error(t('Device.AP.NoSN'))

    return
  }
  console.log('item', item)
  ElMessageBox.confirm(t('Device.AP.DeleteConfirm'), t('Device.AP.Tip'), {
    confirmButtonText: t('Device.AP.Confirm'),
    cancelButtonText: t('Device.AP.Cancel'),
    type: 'warning',
  }).then(() => {
    $api('', {
      requestType: 508,
      data: {
        ap_sn_list: `${item.sn} `,
      },
    }).then(res => {
      if (res.err_code === 0) {
        ElMessage.success(t('Device.AP.DeleteSuccess'))

        // 删除成功后刷新列表数据
        setTimeout(() => {
          getApList()
        }, 1000)
      }
      else {
        ElMessage.error(res.msg || t('Device.AP.DeleteFailed'))
      }
    })
  }).catch(() => {
    ElMessage.info(t('Device.AP.DeleteCancelled'))
  })
}

const getAPMoreList = () => {
  $api('', {
    requestType: 506,
  }).then(() => { })
}

const getApList = () => {
  loading.value = true
  $api('', {
    requestType: 500,
  }).then(res => {
    if (res.err_code === 0) {
      apData.value.total = res.info.ap.length || 0

      // 为每个AP设备添加runstatus字段，默认值为-1
      apData.value.apList = res.info.ap
        .filter((item: any) => item.sn)
        .map((item: any) => ({
          ...item,
          runStatus: -1,
        })) || []

      const models = new Set([]) as Set<string>

      apData.value.apList.forEach((item: any) => {
        if (item.model)
          models.add(item.model)
      })
      modelList.value = Array.from(models).map(item => {
        return {
          label: item,
          value: item,
        }
      })
      modelList.value.unshift({
        label: t('Device.AP.All'),
        value: '',
      })
    }
  }).finally(() => {
    loading.value = false
  })
}

onMounted(() => {
  getApList()
  getAPMoreList()
})

const getAPStatus = () => {
  const selectStr = `${selectedRows.value.join(' ')} ` || ''

  $api('', {
    requestType: 507,
    data: {
      ap_sn_list: selectStr,
    },
  }).then(res => {
    const statusList = res.info.apstatus.filter((item: any) => item.status && item.status !== '0')
    let completedCount = 0 // 移到循环外部

    for (const i in statusList) {
      const status_value = Number(statusList[i].status)
      const sn = statusList[i].sn
      let newStatus = 0
      if (status_value === 1 || status_value === 3) {
        newStatus = 0
      }
      else if (status_value === 2 || status_value === 4) {
        newStatus = 1
        completedCount++
      }
      else if (status_value === 99 || status_value === 999) {
        newStatus = 2
        completedCount++
      }
      else {
        newStatus = 0
      }

      // 根据SN更新表格数组中对应项的runstatus
      const apIndex = apData.value.apList.findIndex((ap: any) => ap.sn === sn)
      if (apIndex !== -1)
        apData.value.apList[apIndex].runStatus = newStatus
    }

    // 如果还有设备未完成，继续轮询
    if (completedCount < statusList.length) {
      console.log(`${completedCount}/${statusList.length} devices completed, continuing polling...`)
      setTimeout(() => {
        getAPStatus()
      }, 2000)
    }
    else {
      console.log('All devices completed!')

      // 5秒后将所有设备的runstatus重置为-1
      setTimeout(() => {
        console.log('Resetting all device runstatus to -1...')
        apData.value.apList.forEach((ap: any) => {
          if (ap.runStatus !== -1)
            ap.runStatus = -1
        })
        console.log('All device runstatus reset completed!')
      }, 5000)
    }
  })
}

// 执行
const execute = () => {
  if (operation.value === '') {
    ElMessage.error(t('Device.AP.SelectOperation'))

    return
  }
  if (selectedRows.value.length === 0) {
    ElMessage.error(t('Device.AP.SelectAP'))

    return
  }

  // 对于需要model参数的操作（如升级），检查型号一致性
  let currentModel = selectedModel.value
  if (!currentModel && operation.value === 2) {
    // 如果没有选择型号，检查选中设备的型号是否一致
    const selectedDevices = apData.value.apList.filter((item: any) =>
      selectedRows.value.includes(item.sn),
    )

    const models = [...new Set(selectedDevices.map((item: any) => item.model))]

    if (models.length > 1) {
      ElMessage.error(t('Tip.errorStr'))

      return
    }
    else if (models.length === 1) {
      currentModel = models[0]
    }
  }
  const selectStr = `${selectedRows.value.join(' ')} ` || ''
  switch (operation.value) {
  case 0:
    // 重启
    $api('', {
      requestType: 501,
      data: {
        ap_sn_list: selectStr,
      },
    }).then(res => {
      if (res.err_code === 0) {
        getAPStatus()
        ElMessage.success(t('Device.AP.RestartSuccess'))
      }
      })
    break
  case 1:
    // 恢复出厂设置
    $api('', {
      requestType: 502,
      data: {
        ap_sn_list: selectStr,
      },
    }).then(res => {
      if (res.err_code === 0)
        ElMessage.success(t('Device.AP.ExecuteSuccess'))
    })
    break
  case 2:
    // 升级
    const file = fileList.value[0]
    const reader = new FileReader()

    reader.onload = (event: any) => {
      const backupFileData = event.target.result.split(',')
      const fileData = backupFileData[1]

      $api('', {
        requestType: 503,
        data: {
          model: currentModel,
          ap_sn_list: selectStr,
          fileName: fileList.value[0].name,
          fileSize: fileList.value[0].size.toString(),
          reset: resetAfterUpdate.value,
          file: fileData,
        },
      }).then(data => {
          if (data.err_code == 0) {
          getAPStatus()
          ElMessage.success(t('Device.AP.UpgradeSuccess'))
        }
      })
    }
    reader.readAsDataURL(file.raw)
    break
  case 3:
    $api('', {
      requestType: 508,
      data: {
        ap_sn_list: selectStr,
      },
    }).then(res => {
      if (res.err_code === 0) {
        ElMessage.success(t('Device.AP.DeleteSuccess'))

          // 批量删除成功后刷新列表数据
          setTimeout(() => {
          getApList()
        }, 1000)

        // 不再清空选中的行
        selectedRows.value = []
      }
      else {
        ElMessage.error(res.msg || t('Device.AP.DeleteFailed'))
      }
    })
    break
  case 4:
    // 闪烁LED
    $api('', {
      requestType: 510,
      data: {
        type: ledMode.value,
        ap_sn_list: selectStr,
      },
    }).then(res => {
      if (res.err_code === 0) {
          getAPStatus()
          ElMessage.success(t('Device.AP.ExecuteSuccess'))
        }
    })
    break
  case 5:
    // 导出 SN和云密码
    $api('', {
      requestType: 511,
      data: {
        ap_sn_list: selectStr,
      },
    }).then(async res => {
      if (res.err_code === 0) {
        const data: any = await $api('', { requestType: 200 })

        console.log('data', data)

        const fileUrl = `${res.info.fileUrl}`
        const fileName = res.info.fileName

        downloadFile(fileUrl, fileName)
      }
    })
    break
  default:
    break
  }
}

const downloadFile = (url: string, fileName: string) => {
  const link = document.createElement('a')

  link.href = url
  link.download = fileName
  link.target = '_blank'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

const openLinkInNewWindow = (url: string) => {
  if (!url)
    return

  window.open(`http://${url}`, '_blank', 'noopener,noreferrer')
}

// 侧边栏
const drawer = ref(false)
const currentTab = ref(0)

const deviceInfoTable: any = ref([])

const wirelessType = ref('2.4G')

const wirelessTypeList = ref([
  {
    label: '2.4G',
    value: '2.4G',
  },
  {
    label: '5G',
    value: '5G',
  },
])

const showCurrentPassword = ref(false)

const roamingParameterFormItems = ref([{
  label: '快速漫游协议（802.11k/v/r）',
  value: '',
  key: 'roaming',
  formType: 'switch',
}, {
  label: '弱信号自动断开阈值',
  value: '',
  key: 'weakSignal',
  formType: 'input',
  subtitle: '推荐范围：-80 ~ -70 dBm',
}, {
  label: 'SSID间隔负载均衡间隔',
  value: '',
  key: 'ssidInterval',
  formType: 'select',
}, {
  label: '忽略弱信号探测',
  value: '',
  key: 'ignoreWeakSignal',
  formType: 'input',
  subtitle: '推荐范围：-90 ~ -80 dBm',
}, {
  label: '忽略过度重传终端',
  value: '',
  key: 'ignoreOverload',
  formType: 'select',
}])

const saveConfig = () => {
  console.log('saveConfig', activeAP.value)
  if (currentTab.value === 0) {
    $api('', {
      requestType: 513,
      data: {
        name: nameModeFormItems.value[0].value,
        ap_sn_list: `${activeAP.value.sn} `,
      },
    }).then(res => {
      if (res.err_code === 0) {
        ElMessage.success(t('Config.Mode.SaveSuccess'))
        drawer.value = false
        getApList()
      }
      else {
        ElMessage.error(res.msg || t('Device.AP.ModeSwitchFailed'))
      }
    })
  }

  if (currentTab.value === 1) {
    if (wirelessFormItems.value[1].value == 'none')
      wirelessFormItems.value[2].value = ''

    if (wirelessFormItems8G.value[1].value == 'none')
      wirelessFormItems8G.value[2].value = ''
    console.log('wirelessFormItems', HTMode.value, selectedBandwidth.value, wifiForce40MHzMode_2G.value)

    let wifiWpa3_2G = '0'
    let wifiWpa3_5G = '0'
    if (wirelessFormItems.value[1].value == 'psk2')
      wifiWpa3_2G = '1'

    if (wirelessFormItems8G.value[1].value == 'psk2')
      wifiWpa3_5G = '1'

    $api('', {
      requestType: 504,
      data: {
        ap_sn_list: `${activeAP.value.sn} `,
        wifiWpa3_2G,
        wifiWpa3_5G,
        ssid_2g: wirelessFormItems.value[0].value,
        ssid_5g: wirelessFormItems8G.value[0].value,
        wifiAuthmode_2G: wirelessFormItems.value[1].value,
        wifiAuthmode_5G: wirelessFormItems8G.value[1].value,
        key_2g: wirelessFormItems.value[2].value,
        key_5g: wirelessFormItems8G.value[2].value,
        wifiOnOff_2G: wirelessFormItems.value[3].value ? '0' : '1',
        wifiOnOff_5G: wirelessFormItems8G.value[3].value ? '0' : '1',
        wifiApIsolate_2G: wirelessFormItems.value[4].value ? '1' : '0',
        wifiApIsolate_5G: wirelessFormItems8G.value[4].value ? '1' : '0',
        wifiHwMode_2G: wirelessFormItems.value[5].value,
        wifiHwMode_5G: wirelessFormItems8G.value[5].value,
        wifiCountry_2G: wirelessFormItems.value[6].value,
        wifiCountry_5G: wirelessFormItems8G.value[6].value,
        wifiChannel_2G: wirelessFormItems.value[7].value,
        wifiChannel_5G: wirelessFormItems8G.value[7].value,
        wifiHtMode_2G: HTMode.value,
        wifiHtMode_5G: wirelessFormItems8G.value[8].value,
        wifiTxpower_2G: wirelessFormItems.value[9].value,
        wifiTxpower_5G: wirelessFormItems8G.value[9].value,
        wifiMaxsta_2G: wirelessFormItems.value[10].value || '',
        wifiMaxsta_5G: wirelessFormItems8G.value[10].value || '',
        wifiForce40MHzMode_2G: wifiForce40MHzMode_2G.value,
      },
    }).then(res => {
      if (res.err_code === 0)
        dealRequestData()
      else
        ElMessage.error(res.msg || t('Device.AP.ModeSwitchFailed'))
    })
  }
  if (currentTab.value === 2) {
    // 验证LAN IP格式
    const lanIpValue = workModeFormItems.value[1].value
    const ipValidationResult = validateLanIp(lanIpValue)

    if (ipValidationResult !== true) {
      ElMessage.error(ipValidationResult)

      return
    }

    $api('', {
      requestType: 504,
      data: {
        net_type: workModeFormItems.value[0].value,
        ap_sn_list: `${activeAP.value.sn} `,
        ap_lan_ip: workModeFormItems.value[1].value,
        ap_lan_mask: workModeFormItems.value[2].value,
      },
    }).then(res => {
      if (res.err_code === 0)
        dealRequestData()
      else
        ElMessage.error(res.msg || t('Device.AP.ModeSwitchFailed'))
    })
  }
  if (currentTab.value === 3) { /* empty */ }
}

const dealRequestData = () => {
  $api('', {
    requestType: 507,
    data: {},
  }).then(res => {
    if (res.err_code === 0) {
      //  获取设置的数组
      const statusList = res.info.apstatus.filter((item: any) => item.status && item.status !== '0')

      //  当前位置只有一个可以直接处理第一个就可以
      const status = Number(statusList[0].status)

      switch (status) {
      case 0:
        // 继续轮询
        setTimeout(() => {
          dealRequestData()
        }, 1000)
        break
      case 1:
        // 继续轮询
        setTimeout(() => {
          dealRequestData()
        }, 1000)
        break
      case 3:
        // 继续轮询
        setTimeout(() => {
          dealRequestData()
        }, 1000)
        break
      case 2:
        // 请求成功，关闭抽屉
        drawer.value = false
        ElMessage.success(t('Device.AP.NoConfigNeeded'))
        break
      case 4:
        // 请求成功，关闭抽屉
        drawer.value = false
        ElMessage.success(t('Device.AP.ConfigSuccess'))
        break
      case 99:
        // 异常结束
        ElMessage.error(t('Device.AP.VerificationTimeout'))
        drawer.value = false
        break
      case 999:
        // 请求超时
        ElMessage.error(t('Device.AP.DeployTimeout'))
        drawer.value = false
        break
      default:
        // 其他错误情况
        ElMessage.error(t('Device.AP.UnknownError'))
        drawer.value = false
        break
      }
    }
    else {
      ElMessage.error(res.msg || t('Device.AP.GetStatusFailed'))
      drawer.value = false
    }
    getApList()
  })
}

const switchNetwork = (rate: number) => {
  if (!rate)
    return '0 B/s'

  // 确保 rate 是数字类型
  const rateNum = Number(rate)
  if (isNaN(rateNum))
    return '0 B/s'

  if (rateNum < 1024)
    return `${rateNum.toFixed(2)} B/s`

  else if (rateNum < 1024 * 1024)
    return `${(rateNum / 1024).toFixed(2)} KB/s`

  else if (rateNum < 1024 * 1024 * 1024)
    return `${(rateNum / (1024 * 1024)).toFixed(2)} MB/s`

  else
    return `${(rateNum / (1024 * 1024 * 1024)).toFixed(2)} GB/s`
}

const selectedBandwidth = ref('20M')
const wifiForce40MHzMode_2G = ref('0')
const HTMode = ref('HT20')

// 处理带宽选择变化的函数
const handleBandwidthChange = value => {
  console.log('bandwidth changed:', value)
  selectedBandwidth.value = value

  // 根据选择的带宽值更新HTMode和wifiForce40MHzMode_2G
  switch (value) {
  case '40M':
    HTMode.value = 'HT40'
    wifiForce40MHzMode_2G.value = '1'
    break
  case '20/40M':
    HTMode.value = 'HT40'
    wifiForce40MHzMode_2G.value = '0'
    break
  case '20M':
  default:
    HTMode.value = 'HT20'
    wifiForce40MHzMode_2G.value = '0'
    break
  }
}

const validatePasswordEight = (value: string) => {
  // 如果加密类型为none，则不验证密码
  if (wirelessFormItems.value[1].value === 'none')
    return true

  // 验证密码长度是否至少为8位
  if (!value || value.length < 8)
    return t('Device.AP.EnterEightDigitPassword')

  return true
}

// 新增：根据5G信道动态计算可用的带宽选项
const BAND_WIDTH_5G_LOCALIZED = computed(() => {
  const channel = wirelessFormItems8G.value[7]?.value

  console.log('channel', channel)

  // 默认情况或自动信道：提供所有带宽选项
  if (!channel || channel === 'auto') {
    return [
      { label: '20M', value: 'HT20' },
      { label: '40M', value: 'HT40' },
      { label: '80M', value: 'HT80' },
      { label: '160M', value: 'HT160' },
    ]
  }

  const channelNum = Number.parseInt(channel, 10)

  // 根据信道范围返回相应的带宽选项
  if (channelNum >= 36 && channelNum <= 128) {
    // 36-128 可选20, 40, 80, 160
    return [
      { label: '20M', value: 'HT20' },
      { label: '40M', value: 'HT40' },
      { label: '80M', value: 'HT80' },
      { label: '160M', value: 'HT160' },
    ]
  }
  else if (channelNum === 132 || channelNum === 136) {
    // 132、136 可选20, 40
    return [
      { label: '20M', value: 'HT20' },
      { label: '40M', value: 'HT40' },
    ]
  }
  else if (channelNum === 140 || channelNum === 144) {
    // 140、144只能选20
    return [
      { label: '20M', value: 'HT20' },
    ]
  }
  else if (channelNum >= 149 && channelNum <= 161) {
    // 149-161 可选 20, 40, 80
    return [
      { label: '20M', value: 'HT20' },
      { label: '40M', value: 'HT40' },
      { label: '80M', value: 'HT80' },
    ]
  }
  else {
    // 161以上可选 20
    return [
      { label: '20M', value: 'HT20' },
    ]
  }
})

const changeChannel = () => {
  const newBandWidthOptions = BAND_WIDTH_5G_LOCALIZED.value
  const currentValue = wirelessFormItems8G.value[8].value
  const isValidOption = newBandWidthOptions.some(option => option.value === currentValue)

  if (!isValidOption && newBandWidthOptions.length > 0) {
    // 如果当前选择不可用，则默认选择列表中第一个选项
    wirelessFormItems8G.value[8].value = newBandWidthOptions[0].value
  }
}

// 排序事件
const sortchange = (val: any) => {
  sortBy.value = val
}

// 监听selectedModel变化，切换型号时默认全选列表
watch(selectedModel, () => {
  // 切换型号时，自动选择当前筛选后的所有设备
  nextTick(() => {
    selectedRows.value = apList.value.map((item: any) => item.sn)
  })
})
</script>

<template>
  <div class="ap-manager">
    <VCard class="mb-6">
      <div class="d-flex flex-wrap gap-4 ma-6">
        <div class="d-flex align-center">
          <div class="cardTitle">
            {{ t('Device.AP.Title') }}
          </div>
        </div>
        <VSpacer />
      </div>
      <VDivider class="mt-4" />
      <div class="pa-6 d-flex">
        <AppSelect
          v-model="selectedModel"
          :items="modelList"
          class="mr-4"
          item-title="label"
          item-value="value"
          :placeholder="t('Config.AP.PleaseSelectModel')"
        />
        <AppSelect
          v-model="operation"
          :items="apHandlers"
          class="mr-4"
          item-title="label"
          item-value="value"
          :placeholder="t('Device.AP.SelectOperation')"
          @update:model-value="operationChange"
        />
        <!-- 升级 -->
        <div
          v-if="operation === 2"
          class="d-flex align-center mr-4"
        >
          <ElUpload
            ref="uploader"
            :auto-upload="false"
            :limit="1"
            :multiple="false"
            :on-change="handleChange"
            :on-exceed="handleExceed"
            :show-file-list="false"
            class="mr-4"
          >
            <span class="mr-4 text-secondary">{{
              fileList && fileList.length ? fileList[0].name : t('Device.AP.NoFileSelected')
            }}</span>
            <VBtn
              color="primary"
              variant="tonal"
            >
              {{ t('Device.AP.SelectFile') }}
            </VBtn>
          </ElUpload>
          <VCheckbox
            v-model="resetAfterUpdate"
            false-value="0"
            :label="t('Device.AP.RestoreFactory')"
            true-value="1"
          />
        </div>
        <!-- led -->
        <div
          v-if="operation === 4"
          class="mr-4"
        >
          <VCheckbox
            v-model="ledMode"
            :label="ledMode === '1' ? t('Device.AP.Blink') : t('Device.AP.Normal')"
            false-value="0"
            true-value="1"
          />
        </div>
        <VBtn @click="execute">
          <VIcon icon="tabler-checks" />
          {{ t('Device.AP.Confirm') }}
        </VBtn>
      </div>
      <VDivider />
      <VDataTable
        v-model="selectedRows"
        v-model:items-per-page="itemsPerPage"
        v-model:page="page"
        :headers="headers"
        :items="apList"
        :items-length="totalProduct"
        :loading="loading"
        class="text-no-wrap"
        item-value="sn"
        show-select
        select-strategy="page"
        :no-data-text="t('NoData')"
        :sort-by="sortBy"
        @update:sort-by="sortchange"
      >
        <template #item.user_name="{ item }">
          <div>{{ item.user_name || '--' }}</div>
        </template>
        <template #item.ip="{ item }">
          <div
            class="text-primary cursor-pointer hover-underline"
            @click="openLinkInNewWindow(item.ip)"
          >
            {{
              item.ip
                || '--'
            }}
          </div>
        </template>
        <template #item.mac="{ item }">
          {{ item.mac || '--' }}
        </template>
        <template #item.onoff="{ item }">
          <template v-if="item.runStatus == -1">
            <VChip
              v-if="item.onoff === 'online'"
              label
              color="success"
              size="small"
            >
              {{ t('Device.AP.Online') }}
            </VChip>
            <VChip
              v-else
              label
              color="error"
              size="small"
            >
              {{ t('Device.AP.Offline') }}
            </VChip>
          </template>
          <template v-else>
            <span v-if="item.runStatus == 0">
              <img
                src="@images/demo_wait.gif"
                alt=""
              >
            </span>
            <span v-if="item.runStatus == 1">
              <img
                width="30"
                height="30"
                src="@images/success.png"
                alt=""
              >
            </span>
            <span v-if="item.runStatus == 2">{{ t('TimeOut') }}</span>
          </template>
        </template>
        <template #item.uptime="{ item }">
          {{ item.runtime || '--' }}
        </template>
        <template #item.actions="{ item }">
          <IconBtn>
            <VIcon icon="tabler-dots-vertical" />
            <VMenu activator="parent">
              <VList>
                <VListItem @click="remoteConfigFun(item)">
                  {{ t('Device.AP.RemoteConfig') }}
                </VListItem>

                <VListItem @click="deviceManagerFun(item)">
                  {{ t('Device.AP.DeviceManagement') }}
                </VListItem>

                <VListItem @click="restartDeviceFun(item)">
                  {{ t('Device.AP.RestartDevice') }}
                </VListItem>

                <VListItem @click="delDeviceFun(item)">
                  {{ t('Device.AP.DeleteDevice') }}
                </VListItem>
              </VList>
            </VMenu>
          </IconBtn>
        </template>

        <template #bottom>
          <TablePagination
            v-model:page="page"
            :items-per-page="itemsPerPage"
            :total-items="totalProduct"
          />
        </template>
      </VDataTable>
    </VCard>

    <VNavigationDrawer
      v-if="drawer"
      v-model="drawer"
      location="right"
      temporary
      persistent
      width="560"
    >
      <div class="h-screen d-flex flex-column">
        <!-- 顶部固定区域 -->
        <div class="flex-shrink-0 d-flex align-center justify-space-between pa-4">
          <div class="text-h5">
            {{ t('Device.AP.DeviceManagement') }}
          </div>
          <VBtn
            color="medium-emphasis"
            icon
            size="small"
            variant="text"
            @click="drawer = false"
          >
            <VIcon
              color="high-emphasis"
              icon="tabler-x"
              size="24"
            />
          </VBtn>
        </div>

        <VDivider />

        <!-- 中间可滚动区域 -->
        <div class="flex-grow-1 overflow-hidden">
          <PerfectScrollbar
            :options="{ wheelPropagation: false }"
            class="h-100"
            tag="div"
          >
            <div class="pa-4 d-flex flex-column h-100">
              <div class="flex-shrink-0">
                <div class="d-flex mb-6">
                  <div
                    class="rounded mr-4"
                    style="block-size: 66px;inline-size: 66px;"
                  >
                    <svg
                      width="66"
                      height="66"
                      viewBox="0 0 66 66"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M54.6616 1H11.3384C5.62867 1 1 5.62867 1 11.3384V54.6616C1 60.3713 5.62867 65 11.3384 65H54.6616C60.3713 65 65 60.3713 65 54.6616V11.3384C65 5.62867 60.3713 1 54.6616 1Z"
                        fill="url(#paint0_linear_1247_23640)"
                      />
                      <path
                        d="M54.6616 1H11.3384C5.62867 1 1 5.62867 1 11.3384V54.6616C1 60.3713 5.62867 65 11.3384 65H54.6616C60.3713 65 65 60.3713 65 54.6616V11.3384C65 5.62867 60.3713 1 54.6616 1Z"
                        stroke="url(#paint1_linear_1247_23640)"
                        stroke-width="1.64103"
                      />
                      <path
                        d="M17.6836 25.3459V40.6622C17.6836 44.8916 21.1123 48.3203 25.3417 48.3203H40.6581C44.8875 48.3203 48.3161 44.8916 48.3161 40.6622V25.3459C48.3161 21.1164 44.8875 17.6877 40.6581 17.6877H25.3417C21.1123 17.6877 17.6836 21.1164 17.6836 25.3459Z"
                        fill="#3773F5"
                        stroke="#3773F5"
                        stroke-width="2.05128"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M36.8267 25.3457H29.168"
                        stroke="white"
                        stroke-width="2.05128"
                        stroke-linejoin="round"
                      />
                      <defs>
                        <linearGradient
                          id="paint0_linear_1247_23640"
                          x1="33"
                          y1="0.179487"
                          x2="33"
                          y2="65.8205"
                          gradientUnits="userSpaceOnUse"
                        >
                          <stop stop-color="#F0F5FF" />
                          <stop
                            offset="1"
                            stop-color="#EFF4FF"
                          />
                        </linearGradient>
                        <linearGradient
                          id="paint1_linear_1247_23640"
                          x1="33"
                          y1="0.179487"
                          x2="33"
                          y2="64.1795"
                          gradientUnits="userSpaceOnUse"
                        >
                          <stop
                            stop-color="#E1EBFF"
                            stop-opacity="0"
                          />
                          <stop
                            offset="1"
                            stop-color="#DEE9FF"
                          />
                        </linearGradient>
                      </defs>
                    </svg>
                  </div>
                  <div class="d-flex flex-column justify-space-around">
                    <div class="d-flex align-center">
                      <span class="text-h5">{{ activeAP.user_name || '--' }}</span>
                      <VChip
                        v-if="activeAP.onoff === 'online'"
                        class="ml-2"
                        color="success"
                        size="small"
                      >
                        {{ t('Device.AP.Online') }}
                      </VChip>
                      <VChip
                        v-else
                        class="ml-2"
                        color="error"
                        size="small"
                      >
                        {{ t('Device.AP.Offline') }}
                      </VChip>
                    </div>
                    <div class="d-flex align-center text-subtitle-1">
                      <div class="text-primary mr-2">
                        <VIcon
                          icon="tabler-arrow-narrow-down"
                          size="x-small"
                        />
                        {{ switchNetwork(activeAP.tx_rate) }}
                      </div>
                      <div class="text-success">
                        <VIcon
                          icon="tabler-arrow-narrow-up"
                          size="x-small"
                        />
                        {{ switchNetwork(activeAP.rx_rate) }}
                      </div>
                    </div>
                  </div>
                </div>

                <VTabs v-model="currentTab">
                  <VTab :value="0">
                    {{ t('Device.AP.DeviceInfo') }}
                  </VTab>
                  <VTab :value="1">
                    {{ t('Device.AP.WirelessStatus') }}
                  </VTab>
                  <VTab :value="2">
                    {{ t('Device.AP.WorkMode') }}
                  </VTab>
                </VTabs>
              </div>

              <div class="flex-grow-1 overflow-hidden mt-4">
                <VWindow
                  v-model="currentTab"
                  class="h-100 d-flex flex-column"
                >
                  <VWindowItem
                    :value="0"
                    class="flex-grow-1 overflow-auto"
                    style=" -ms-overflow-style: none;scrollbar-width: none;"
                  >
                    <div class="bg-grey-light pa-4 my-4 rounded">
                      <VRow>
                        <VCol>
                          <div class="label">
                            {{ t('Device.AP.InternetStatus') }}
                          </div>
                          <div
                            v-if="activeAP.internet === '0'"
                            class="value text-success"
                          >
                            {{ t('Device.AP.Online') }}
                          </div>
                          <div
                            v-else
                            class="value text-error"
                          >
                            {{ t('Device.AP.Offline') }}
                          </div>
                        </VCol>
                        <VCol>
                          <div class="label">
                            {{ t('Device.AP.RunningTime') }}
                          </div>
                          <div class="value">
                            {{ activeAP.runtime }}
                          </div>
                        </VCol>
                        <VCol>
                          <div class="label">
                            {{ t('Device.AP.WorkingMode') }}
                          </div>
                          <div class="value">
                            {{ activeAP.net_type === '0' ? t('Device.AP.RouterMode') : t('Device.AP.APMode') }}
                          </div>
                        </VCol>
                      </VRow>
                    </div>
                    <VForm>
                      <VForm v-if="wirelessType === '2.4G'">
                        <div
                          v-for="(item, index) in nameModeFormItems"
                          :key="index"
                          class="d-flex align-start mb-4"
                        >
                          <div class="w-80 h-38 flex-0-0 mr-4 text-subtitle-2">
                            {{ item.label }}
                          </div>
                          <div class="w-100">
                            <AppTextField
                              v-if="item.formType === 'input'"
                              v-model="item.value"
                              :rules="item.rules"
                              append-inner-icon="tabler-edit"
                            />
                            <div
                              v-if="item.subtitle"
                              class="text-subtitle-2"
                            >
                              {{ item.subtitle }}
                            </div>
                          </div>
                        </div>
                      </VForm>
                    </VForm>
                    <div>
                      <div
                        v-for="(item, index) in deviceInfoTable"
                        :key="index"
                        class="d-flex h-38 mb-4"
                      >
                        <div class="flex-0-0 mr-4 w-80 text-on-surface opacity-90 text-subtitle-2">
                          {{ item.label }}
                        </div>
                        <div class="flex-1-0 text-secondary opacity-70 text-subtitle-1">
                          {{ item.value }}
                        </div>
                      </div>
                    </div>
                  </VWindowItem>
                  <VWindowItem
                    :value="1"
                    class="flex-grow-1 overflow-auto"
                    style=" -ms-overflow-style: none;scrollbar-width: none;"
                  >
                    <div class="d-flex flex-column h-100">
                      <div class="flex-shrink-0">
                        <BtnGroupSelector
                          v-model:value="wirelessType"
                          fill-row
                          :options="wirelessTypeList"
                          class="my-4"
                        />
                      </div>
                      <div
                        class="flex-grow-1 overflow-auto"
                        style=" -ms-overflow-style: none;scrollbar-width: none;"
                      >
                        <div class="wireless-form">
                          <VForm v-if="wirelessType === '2.4G'">
                            <div
                              v-for="(item, index) in wirelessFormItems"
                              :key="index"
                              class="d-flex align-start mb-4"
                            >
                              <div class="w-80 h-38 flex-0-0 mr-4 text-subtitle-2">
                                {{ item.label }}
                              </div>
                              <div class="w-100">
                                <AppTextField
                                  v-if="item.formType === 'input'"
                                  v-model="item.value"
                                  :rules="item.rules"
                                  append-inner-icon="tabler-edit"
                                />
                                <AppSelect
                                  v-else-if="item.formType === 'select'"
                                  v-model="item.value"
                                  :items="item.list"
                                  item-title="label"
                                  @update:model-value="val => item.key === 'bandwidth' ? handleBandwidthChange(val) : undefined"
                                />
                                <AppTextField
                                  v-else-if="item.formType === 'password'"
                                  v-model="item.value"
                                  :append-inner-icon="showCurrentPassword ? 'tabler-eye-off' : 'tabler-eye'"
                                  :rules="item.rules ? item.rules : [requiredValidator]"
                                  :type="showCurrentPassword ? 'text' : 'password'"
                                  @click:append-inner="showCurrentPassword = !showCurrentPassword"
                                />
                                <div
                                  v-else-if="item.formType === 'switch'"
                                  class="d-flex align-center justify-end"
                                >
                                  <VSwitch
                                    v-model="item.value"
                                    class="mr-2"
                                  />
                                  <span class="text-subtitle-2">{{ item.value ? t('Device.AP.Enable') : t('Device.AP.Disable') }}</span>
                                </div>
                                <div
                                  v-if="item.subtitle"
                                  class="text-subtitle-2"
                                >
                                  {{ item.subtitle }}
                                </div>
                              </div>
                            </div>
                          </VForm>
                          <VForm v-if="wirelessType === '5G'">
                            <div
                              v-for="(item, index) in wirelessFormItems8G"
                              :key="index"
                              class="d-flex align-start mb-4"
                            >
                              <div class="w-80 h-38 flex-0-0 mr-4 text-subtitle-2">
                                {{ item.label }}
                              </div>
                              <div class="w-100">
                                <AppTextField
                                  v-if="item.formType === 'input'"
                                  v-model="item.value"
                                  :rules="item.rules"
                                  append-inner-icon="tabler-edit"
                                />
                                <AppSelect
                                  v-else-if="item.formType === 'select'"
                                  v-model="item.value"
                                  :items="item.list"
                                  item-title="label"
                                  @update:model-value="val => item.key === 'channel' ? changeChannel() : undefined"
                                />
                                <AppTextField
                                  v-else-if="item.formType === 'password'"
                                  v-model="item.value"
                                  :append-inner-icon="showCurrentPassword ? 'tabler-eye-off' : 'tabler-eye'"
                                  :rules="item.rules ? item.rules : [requiredValidator]"
                                  :type="showCurrentPassword ? 'text' : 'password'"
                                  @click:append-inner="showCurrentPassword = !showCurrentPassword"
                                />
                                <div
                                  v-else-if="item.formType === 'switch'"
                                  class="d-flex align-center justify-end"
                                >
                                  <VSwitch
                                    v-model="item.value"
                                    class="mr-2"
                                  />
                                  <span class="text-subtitle-2">{{ item.value ? t('Device.AP.Enable') : t('Device.AP.Disable') }}</span>
                                </div>
                                <div
                                  v-if="item.subtitle"
                                  class="text-subtitle-2"
                                >
                                  {{ item.subtitle }}
                                </div>
                              </div>
                            </div>
                          </VForm>
                        </div>
                      </div>
                    </div>
                  </VWindowItem>
                  <VWindowItem
                    :value="2"
                    class="flex-grow-1 overflow-auto"
                    style=" -ms-overflow-style: none;scrollbar-width: none;"
                  >
                    <VForm>
                      <div
                        v-for="(item, index) in workModeFormItems"
                        :key="index"
                        class="d-flex align-start mt-4"
                      >
                        <div class="w-80 h-38 flex-0-0 mr-4 text-subtitle-2">
                          {{ item.label }}
                        </div>
                        <div class="w-100">
                          <AppTextField
                            v-if="item.formType === 'input'"
                            v-model="item.value"
                            :rules="item.rules"
                            append-inner-icon="tabler-edit"
                          />
                          <AppSelect
                            v-else-if="item.formType === 'select'"
                            v-model="item.value"
                            :items="item.list"
                            :rules="item.rules"
                            item-title="label"
                          />
                          <AppTextField
                            v-else-if="item.formType === 'password'"
                            v-model="item.value"
                            :append-inner-icon="showCurrentPassword ? 'tabler-eye-off' : 'tabler-eye'"
                            :rules="[requiredValidator]"
                            :type="showCurrentPassword ? 'text' : 'password'"
                            @click:append-inner="showCurrentPassword = !showCurrentPassword"
                          />
                          <div
                            v-else-if="item.formType === 'switch'"
                            class="d-flex align-center justify-end"
                          >
                            <VSwitch
                              v-model="item.value"
                              class="mr-2"
                            />
                            <span class="text-subtitle-2">{{ item.value ? t('Device.AP.Enable') : t('Device.AP.Disable') }}</span>
                          </div>
                          <div
                            v-else-if="item.formType === 'text'"
                            class="h-38"
                          >
                            {{ item.value }}
                          </div>
                          <div
                            v-if="item.subtitle"
                            class="text-subtitle-2"
                          >
                            {{ item.subtitle }}
                          </div>
                        </div>
                      </div>
                    </VForm>
                  </VWindowItem>
                </VWindow>
              </div>
            </div>
          </PerfectScrollbar>
        </div>

        <!-- 底部固定区域 -->
        <VDivider />
        <div class="flex-shrink-0 pa-4 d-flex align-center justify-space-between">
          <VBtn
            color="primary"
            variant="outlined"
            @click="remoteConfigFun(activeAP)"
          >
            {{ t('Device.AP.RemoteConfig') }}
          </VBtn>
          <div>
            <VBtn
              class="mr-4"
              color="secondary"
              variant="tonal"
              @click="drawer = false"
            >
              {{ t('Device.AP.Cancel') }}
            </VBtn>
            <VBtn
              color="primary"
              @click="saveConfig"
            >
              {{ t('Device.AP.Save') }}
            </VBtn>
          </div>
        </div>
      </div>
    </VNavigationDrawer>
  </div>
</template>

<style lang="scss" scoped>
.flexBox {
  display: flex;
  align-items: center;
}

.cardTitle {
  font-family: "PingFang SC", serif;
  font-size: 18px;
  font-weight: 600;
  line-height: 28px;
}

.ap-manager {
  .label {
    color: #999;
    font-size: 13px;
    margin-block-end: 2px;
  }

  .value {
    color: rgba($color: var(--v-theme-on-surface), $alpha: 90%);
    font-size: 15px;
  }

  .sub-title {
    color: rgba($color: var(--v-theme-on-surface), $alpha: 55%);
    font-size: 15px;
    font-weight: normal;
  }

  .rounded-progress {
    border-end-start-radius: 10px !important;
    border-start-start-radius: 10px !important;
  }

  .h-38 {
    block-size: 38px;
    line-height: 38px;
  }

  .w-80 {
    inline-size: 80px;
  }
}
</style>
