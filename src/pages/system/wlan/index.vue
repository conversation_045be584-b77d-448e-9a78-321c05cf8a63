<script lang="ts" setup>
const mode = ref(0)
const channelOptimization = ref(false)
const frequencyBand = ref(0)
const frequencyBandBandwidth = ref(0)
const channelSet = ref(0)
const powerTuning = ref(false)
const timeTuning = ref(false)
</script>

<template>
  <div>
    <div
      class="mb-4 text-subtitle-2 text-primary pa-3 bg-primary-semi-transparent rounded-sm d-flex align-center border border-primary">
      <v-icon class="mr-2" icon="tabler-info-circle" color="secondary"></v-icon>
      更改无线设置可能会暂时中断无线连接。建议在网络使用较少时进行更改。
    </div>
    <v-card class="pa-4">
      <v-radio-group v-model="mode">
        <v-row class="mb-2">
          <v-col>
            <div
              :class="['pa-4 rounded-sm d-flex flex-column align-center', mode === 0 ? 'border-fill-primary' : 'border']">
              <v-icon icon="tabler-bolt" size="25" color="primary" class="mb-4"></v-icon>
              <div class="title text-h6 mb-2">
                智能模式
              </div>
              <div class="sub-title text-subtitle-2 mb-4">
                自动调整无线模式，适应终端与AP数量。
              </div>
              <v-radio color="primary" :value="0"></v-radio>
            </div>
          </v-col>
          <v-col>
            <div
              :class="['pa-4 rounded-sm d-flex flex-column align-center', mode === 1 ? 'border-fill-primary' : 'border']">
              <v-icon icon="tabler-bolt" size="25" color="primary" class="mb-4"></v-icon>
              <div class="title text-h6 mb-2">
                高性能模式
              </div>
              <div class="sub-title text-subtitle-2 mb-4">
                {{ '终端<30, 统一信道与频宽，实现高吞吐量与无缝漫游 ' }}
              </div>
                <v-radio color="primary" :value="1"></v-radio>
              </div>
          </v-col>
          <v-col>
            <div :class="[' pa-4 rounded-sm d-flex flex-column align-center', mode === 2 ? 'border-fill-primary'
              : 'border']">
                  <v-icon icon="tabler-bolt" size="25" color="primary" class="mb-4"></v-icon>
                  <div class="title text-h6 mb-2">
                    高带机量模式
                  </div>
                  <div class="sub-title text-subtitle-2 mb-4">
                    终端 ≥30，错开信道，提高网络承载能力
                  </div>
                  <v-radio color="primary" :value="2"></v-radio>
              </div>
          </v-col>
        </v-row>
      </v-radio-group>
      <div class="mb-4 pa-4 rounded-sm border d-flex justify-space-between align-center">
        <div>
          <div class="text-h6">信道调优</div>
          <div class="text-subtitle-2">启用信道调优后会根据设置自动优化当前网络</div>
        </div>
        <v-switch v-model="channelOptimization"></v-switch>
      </div>
      <div class="mb-4 w-full border-fill-primary rounded d-flex">
        <div
          :class="['flex-1-0 rounded-e-0 text-center py-2 cursor-pointer text-primary transition', frequencyBand === 0 ? 'bg-primary-semi-transparent' : '']"
          @click="frequencyBand = 0">2.4G
        </div>
        <div class="h-full bg-primary" style="width: 1px;flex-shrink: 0;"></div>
        <div
          :class="['flex-1-0 rounded-s-0 text-center py-2 cursor-pointer text-primary transition', frequencyBand === 1 ? 'bg-primary-semi-transparent' : '']"
          @click="frequencyBand = 1">5G
        </div>
      </div>
      <div class="text-h6 mb-2">频段带宽</div>
      <div class="mb-4">
        <v-row>
          <v-col>
            <div
              :class="['pa-4 rounded-sm cursor-pointer', frequencyBandBandwidth === 0 ? 'border-fill-surface' : 'border']"
              @click="frequencyBandBandwidth = 0">
              <div class="text-h6">20 MHz</div>
              <div class="text-subtitle-2">减少干扰，提高稳定性</div>
            </div>
          </v-col>
          <v-col>
            <div
              :class="['pa-4 rounded-sm cursor-pointer', frequencyBandBandwidth === 1 ? 'border-fill-surface' : 'border']"
              @click="frequencyBandBandwidth = 1">
              <div class="text-h6">40 MHz</div>
              <div class="text-subtitle-2">平衡速度和稳定性</div>
            </div>
          </v-col>
          <v-col>
            <div
              :class="['pa-4 rounded-sm cursor-pointer', frequencyBandBandwidth === 2 ? 'border-fill-surface' : 'border']"
              @click="frequencyBandBandwidth = 2">
              <div class="text-h6">80 MHz</div>
              <div class="text-subtitle-2">最高速度，适合高性能需求</div>
            </div>
          </v-col>
        </v-row>
      </div>
      <div class="text-h6 mb-2">信道集合</div>
      <div class="mb-4">
        <v-row>
          <v-col>
            <div :class="['pa-4 rounded-sm cursor-pointer', channelSet === 0 ? 'border-fill-surface' : 'border']"
              @click="channelSet = 0">
              <div>1,6,11</div>
            </div>
          </v-col>
          <v-col>
            <div :class="['pa-4 rounded-sm cursor-pointer', channelSet === 1 ? 'border-fill-surface' : 'border']"
              @click="channelSet = 1">
              <div>1,5,9,13</div>
            </div>
          </v-col>
          <!-- 5G配置 -->
          <!-- <v-col>
            <div class="pa-4 border-fill-surface rounded-sm cursor-pointer">
              <div>36，44，149，157</div>
            </div>
          </v-col>
          <v-col>
            <div class="pa-4 border rounded-sm cursor-pointer">
              <div>40，48，153，161</div>
            </div>
          </v-col>
          <v-col>
            <div class="pa-4 border rounded-sm cursor-pointer">
              <div>36，48，149，161</div>
            </div>
          </v-col> -->
        </v-row>
      </div>
      <div class="mb-4 pa-4 rounded-sm border">
        <div :class="['d-flex justify-space-between align-center', powerTuning ? 'mb-2' : '']">
          <div class="text-h6">功率调优</div>
          <v-switch v-model="powerTuning" :true-value="true" :false-value="false"></v-switch>
        </div>
        <div v-show="powerTuning">
          <v-row>
            <v-col>
              <div class="text-h6 mb-2">覆盖阈值（dBM）</div>
              <AppTextField value="12" placeholder="请输入覆盖阈值" />
            </v-col>
            <v-col>
              <div class="text-h6 mb-2">最大功率（dBM）</div>
              <AppTextField value="12" placeholder="请输入最大功率" />
            </v-col>
            <v-col>
              <div class="text-h6 mb-2">最小功率（dBM）</div>
              <AppTextField value="12" placeholder="请输入最小功率" />
            </v-col>
          </v-row>
        </div>
      </div>
      <div class="mb-4 pa-4 rounded-sm border">
        <div :class="['d-flex justify-space-between align-center', timeTuning ? 'mb-2' : '']">
          <div class="text-h6">定时调优</div>
          <v-switch v-model="timeTuning"></v-switch>
        </div>
        <div v-show="timeTuning">
          <v-row>
            <v-col>
              <div class="text-h6 mb-2">日期</div>
              <AppSelect :items="DATE_FREQUENCY" item-title="label" placeholder="" />
            </v-col>
            <v-col>
              <div class="text-h6 mb-2">时间</div>
              <AppSelect :items="DATE_FREQUENCY" item-title="label" placeholder="" />
            </v-col>
          </v-row>
        </div>
      </div>
      <div class="mb-4 pa-4 rounded-sm border d-flex justify-space-between align-center">
        <div class="text-h6">射频调优</div>
        <v-btn variant="outlined">立即调优</v-btn>
      </div>
      <div class="d-flex justify-end">
        <v-btn class="mr-4" color="secondary">取消</v-btn>
        <v-btn>保存设置</v-btn>
      </div>
    </v-card>
  </div>
</template>

<style lang="scss">
.bg-primary-semi-transparent {
  background-color: rgba(var(--v-theme-primary), 0.08) !important;
}
</style>
