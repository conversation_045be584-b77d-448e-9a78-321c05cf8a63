<script lang="ts" setup>
import { useI18n } from 'vue-i18n'
import Reset from '@/components/system/systemConfig/reset.vue'
import Restart from '@/components/system/systemConfig/restart.vue'
import Safe from '@/components/system/systemConfig/safe.vue'
import System from '@/components/system/systemConfig/system.vue'
import Update from '@/components/system/systemConfig/update.vue'

const { t } = useI18n()

const activeTab = ref(0)

const tabList = ref([
  {
    text: t('SystemConfig.Tabs.SystemSettings'),
    value: 0,
  },
  {
    text: t('SystemConfig.Tabs.SecurityManagement'),
    value: 1,
  },
  {
    text: t('SystemConfig.Tabs.UpgradeBackup'),
    value: 2,
  },
  {
    text: t('SystemConfig.Tabs.RestartDevice'),
    value: 3,
  },
  {
    text: t('SystemConfig.Tabs.FactoryReset'),
    value: 4,
  },
])
</script>

<template>
  <div class="tabBox">
    <div
      v-for="item in tabList"
      :key="item.value"
      class="tabText"
      @click="activeTab = item.value"
    >
      <VBtn
        :variant="activeTab === item.value ? 'flat' : 'text'"
        :color="activeTab === item.value ? 'primary' : 'secondary'"
      >
        {{ item.text }}
      </VBtn>
    </div>
  </div>
  <div class="tabPage">
    <System v-if="activeTab === 0" />
    <Safe v-if="activeTab === 1" />
    <Update v-if="activeTab === 2" />
    <Restart v-if="activeTab === 3" />
    <Reset v-if="activeTab === 4" />
  </div>
</template>

<style lang="scss" scoped>
.tabBox {
  display: flex;
  margin-block-end: 10px;
}
</style>
