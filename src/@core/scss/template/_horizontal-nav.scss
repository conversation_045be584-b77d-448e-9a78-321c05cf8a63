@use "@core/scss/template/placeholders" as *;

.layout-horizontal-nav {
  color: rgba(var(--v-theme-on-background), var(--v-high-emphasis-opacity));

  // SECTION Nav Group
  .nav-group,
  .nav-link {
    .popper-content {
      .nav-link.sub-item a,
      .nav-group-label {
        @extend %nav-group-label-and-nav-link-style;
      }

      .nav-group.active {
        > .popper-triggerer .nav-group-label {
          font-weight: 500;
        }
      }

      .nav-group.sub-item .sub-item {
        .nav-group-label .nav-item-icon {
          color: rgba(var(--v-theme-on-surface), var(--v-medium-emphasis-opacity));
        }

        a:not(.router-link-exact-active) .nav-item-icon {
          color: rgba(var(--v-theme-on-surface), var(--v-medium-emphasis-opacity));
        }
      }

      .nav-link.sub-item {
        .router-link-active.router-link-exact-active {
          font-weight: 500;
        }
      }
    }
  }

  // !SECTION
}
