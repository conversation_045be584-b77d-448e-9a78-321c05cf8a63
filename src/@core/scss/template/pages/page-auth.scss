.layout-blank {
  .auth-wrapper {
    min-block-size: 100dvh;
  }

  .auth-v1-top-shape,
  .auth-v1-bottom-shape {
    position: absolute;
  }

  .auth-footer-mask {
    position: absolute;
    inset-block-end: 0;
    min-inline-size: 100%;
  }

  .auth-card {
    z-index: 1 !important;
  }

  .auth-illustration {
    z-index: 1;
  }

  .auth-v1-top-shape {
    inset-block-start: -77px;
    inset-inline-start: -45px;
  }

  .auth-v1-bottom-shape {
    inset-block-end: -58px;
    inset-inline-end: -58px;
  }

  @media (min-width: 1264px), (max-width: 959px) and (min-width: 450px) {
    .v-otp-input .v-otp-input__content {
      gap: 1rem;
    }
  }
}

@media (min-width: 960px) {
  .skin--bordered {
    .auth-card-v2 {
      border-inline-start: 1px solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
    }
  }
}

.auth-logo {
  position: absolute;
  z-index: 2;
  inset-block-start: 2rem;
  inset-inline-start: 2.3rem;
}

.auth-title {
  font-size: 1.375rem;
  font-weight: 700;
  letter-spacing: 0.25px;
  line-height: 1.5rem;
  text-transform: capitalize;
}
