@use "@core/scss/base/mixins";
@use "@configured-variables" as variables;

// 👉 Radio
.v-radio,
.v-radio-btn {
  // 👉 radio icon opacity
  .v-selection-control__input > .v-icon {
    opacity: 1;
  }

  &.v-selection-control--disabled {
    --v-disabled-opacity: 0.45;
  }

  &.v-selection-control--dirty {
    @each $color-name in variables.$theme-colors-name {
      .v-selection-control__wrapper.text-#{$color-name} {
        .v-selection-control__input {
          /* ℹ️ Using filter: drop-shadow() instead of box-shadow because box-shadow creates white background for SVG; */
          .v-icon {
            filter: drop-shadow(0 2px 6px rgba(var(--v-theme-#{$color-name}), 0.3));
          }
        }
      }
    }
  }

  &.v-selection-control {
    .v-selection-control__input {
      svg {
        font-size: 1.5rem;
      }
    }

    .v-label {
      color: rgba(var(--v-theme-on-background), var(--v-high-emphasis-opacity));
    }
  }
}

// 👉 Radio, Checkbox

.v-input.v-radio-group > .v-input__control > .v-label {
  margin-inline-start: 0;
}
