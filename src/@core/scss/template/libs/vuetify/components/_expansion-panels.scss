@use "@core/scss/base/mixins";
@use "@layouts/styles/mixins" as layoutsMixins;

// 👉 Expansion panels
body .v-layout .v-application__wrap .v-expansion-panels {
  .v-expansion-panel {
    margin-block-start: 0 !important;

    // expansion panel arrow font size
    .v-expansion-panel-title {
      color: rgba(var(--v-theme-on-surface), var(--v-high-emphasis-opacity));
      font-weight: 500;

      .v-expansion-panel-title__icon {
        transition: transform 0.2s ease-in-out;

        .v-icon {
          block-size: 1.25rem !important;
          font-size: 1.25rem !important;
          inline-size: 1.25rem !important;
        }
      }
    }

    .v-expansion-panel-title,
    .v-expansion-panel-title--active,
    .v-expansion-panel-title:hover,
    .v-expansion-panel-title:focus,
    .v-expansion-panel-title:focus-visible,
    .v-expansion-panel-title--active:focus,
    .v-expansion-panel-title--active:hover {
      .v-expansion-panel-title__overlay {
        opacity: 0 !important;
      }
    }

    // Set Elevation when panel open
    &:not(.v-expansion-panels--variant-accordion) {
      &.v-expansion-panel--active {
        .v-expansion-panel__shadow {
          @include mixins.elevation(6);
        }
      }
    }
  }

  // custom style for expansion panels
  &.expansion-panels-width-border {
    border: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
    border-radius: 0.375rem;

    .v-expansion-panel-title {
      background-color: rgb(var(--v-theme-grey-light));
      border-block-end: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
      margin-block-end: -1px;
    }

    .v-expansion-panel-text {
      .v-expansion-panel-text__wrapper {
        padding: 1.25rem;
      }
    }
  }

  &:not(.no-icon-rotate, .expansion-panels-width-border) {
    .v-expansion-panel {
      .v-expansion-panel-title__icon {
        .v-icon {
          @include layoutsMixins.rtl {
            transform: scaleX(-1);
          }
        }
      }

      &.v-expansion-panel--active {
        .v-expansion-panel-title__icon {
          transform: rotate(90deg);

          @include layoutsMixins.rtl {
            transform: rotate(-90deg);
          }
        }
      }
    }
  }

  &:not(.expansion-panels-width-border) {
    .v-expansion-panel {
      &:not(:last-child) {
        margin-block-end: 0.5rem;
      }

      &:not(:first-child)::after {
        content: none;
      }

      // ℹ️ we have to use below style of increase the specificity and override the default style
      /* stylelint-disable-next-line no-descending-specificity */
      &:first-child:not(:last-child),
      &:not(:first-child, :last-child),
      &:not(:first-child) {
        border-radius: 0.375rem !important;
      }
    }
  }
}
